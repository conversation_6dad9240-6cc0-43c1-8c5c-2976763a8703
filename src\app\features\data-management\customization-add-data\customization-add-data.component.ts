import {
  AfterViewInit,
  Component,
  EventEmitter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BehaviorSubject, combineLatest } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  skipWhile,
  takeUntil,
} from 'rxjs/operators';
import {
  BHK_NO,
  BHK_NO_ALL,
  BHK_TYPE,
  EMPTY_GUID,
  FURNISH_STATUS,
  GENDER,
  MARITAL_STATUS,
  MONTHS,
  NUMBER_10,
  OFFERING_TYPE,
  POSSESSION_DATE_FILTER_LIST,
  POSSESSION_NEEDED_BY_LIST,
  PURPOSE_LIST,
  TRANSACTION_TYPE_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BHKType,
  EnquiryType,
  FurnishStatus,
  PossessionType,
  Profession,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  allowLandlineInput,
  assignToSort,
  changeCalendar,
  convertUrlsToLinks,
  formatBudget,
  generateFloorOptions,
  getBHKDisplayString,
  getFormattedLocation,
  getLocalityDetailsByObj,
  getLocationDetailsByObj,
  getPropertyTypeIds,
  getTimeZoneDate,
  onlyNumbers,
  onPickerOpened,
  patchTimeZoneDate,
  setPropertySubTypeList,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  AddNewData,
  ClearData,
  FetchDataById,
  FetchDataIdWithAltNo,
  FetchDataIdWithContactNo,
  FetchDataSourceList,
  FetchDataSubSourceList,
  UpdateData,
} from 'src/app/reducers/data/data-management.actions';
import {
  getActiveData,
  getActiveDataIsLoading,
  getDataIsAdding,
  getDataIsUpdating,
  getDataSourceList,
  getDataSourceListIsLoading,
  getDataSubSourceList,
  getDataSubSourceListIsLoading,
} from 'src/app/reducers/data/data-management.reducer';
import {
  getAllSources,
  getAllSourcesLoading,
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getAgencyNameListIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchProjectList,
  FetchPropertyList
} from 'src/app/reducers/lead/lead.actions';
import {
  getCampaignList,
  getCampaignListIsLoading,
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getPropertyList,
  getPropertyListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';

import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits,
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  FetchInternationalLocations,
  FetchLocationsWithGoogle
} from 'src/app/reducers/site/site.actions';
import {
  getInternationalLocations,
  getInternationLocationsIsLoading,
  getLocationsWithGoogleApi,
} from 'src/app/reducers/site/site.reducer';

import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';

import { countries } from 'countries-list';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';

@Component({
  selector: 'customization-add-data',
  templateUrl: './customization-add-data.component.html',
})
export class CustomizationAddDataComponent
  implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('previewModal') previewModal: TemplateRef<any>;
  enquiredForList: Array<Object> = TRANSACTION_TYPE_LIST;
  propertySubType: Array<Object> = [
    'Independent House',
    'Flat',
    'Villa',
    'Plot',
    'Row Villa',
  ];
  bhkNoList: Array<string> = BHK_NO;
  numbers: Array<{ display: string; value: number }> = NUMBER_10;
  numbers10: Array<{ display: string; value: number }> = NUMBER_10.slice(1);
  propertyTypeIcon: any[] = JSON.parse(localStorage.getItem('propertyType'));
  propTypeIcon: {} = [
    { name: 'Residential', icon: 'ic-home-secondary' },
    { name: 'Commercial', icon: 'ic-building-secondary' },
    { name: 'Agricultural', icon: 'ic-leaf' },
  ];
  offerType: Array<{ displayName: string; value: number }> = OFFERING_TYPE;
  furnishStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  purposeList: Array<{ displayName: string; value: number }> = PURPOSE_LIST;
  bhkTypes: Array<string> = BHK_TYPE;
  selectedDataId: any;
  selectedDataInfo: any;
  hasInternationalSupport: boolean = false;
  preferredCountries = ['in'];
  addDataForm: FormGroup;
  checkDuplicacy: boolean = false;
  checkAlternateNumDuplicacy: boolean = false;
  numberEdited: boolean = true;
  alternateNumberEdited: boolean = true;
  sourceList: Array<any>;
  sourceIdMap: any = {};
  subSourceList: Record<string, string[]> = {};
  isSubSourceListLoading: boolean = true;
  subSources: string[] = [];
  canEdit: boolean = false;
  canAssign: boolean = false;
  canViewComponent: boolean = false;
  canAdminEditSource: boolean;
  allUsers: any[];
  users: any;
  isUsersLoading: boolean = true;
  activeUsers: any;
  inactiveUsers: any[];
  placesList: any[] = [];
  currency: any[] = [];
  defaultCurrency: string = 'INR';
  countryCode: any[];
  timeZone: any[];
  lowerBudgetInWords: string = '';
  upperBudgetInWords: string = '';
  budgetValidation: boolean = false;
  syncingCurrency: boolean = false;
  areaSizeUnits: Array<any>;
  onlyNumbers = onlyNumbers;
  getBHKDisplayString = getBHKDisplayString;
  getLocationDetailsByObj = getLocationDetailsByObj;
  getFormattedLocation = getFormattedLocation;
  convertUrlsToLinks = convertUrlsToLinks
  floorOptions: string[] = generateFloorOptions();
  propertyTypeList: any[] = JSON.parse(localStorage.getItem('propertyType'));
  propSubTypes: Array<{ displayName: string }> = [];
  projectList: Array<string> = [];
  isProjectListLoading: boolean = true;
  isGlobalSettingsLoading: boolean = true;
  propertyList: Array<string> = [];
  isPropertyListLoading: boolean = true;
  agencyNameList: Array<string> = [];
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  channelPartnerList: Array<any>;
  channelPartnerListIsLoading: boolean = true;
  profession: Array<Profession | string> = Object.values(Profession).slice(
    1,
    10
  );
  duplicateDataId: any;
  duplicateDataAltId: any;
  isLoadingHasDataAltInfo: any;
  carpetAreaConversion: string;
  saleableAreaConversion: string;
  builtUpAreaConversion: string;
  propertyAreaConversion: string;
  netAreaConversion: string;
  canNavigate: boolean = false;
  canAltNavigate: boolean = false;
  currFormValues: any;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  showLeftNav: boolean = true;
  @ViewChild('contactNoInput') contactNoInput: any;
  @ViewChild('alternateNoInput') alternateNoInput: any;
  @ViewChild('referralNoInput') referralNoInput: any;
  isShowManualLocation: boolean = false;
  isShowManualCustomerLocation: boolean = false;
  internationalLocations: any[] = [];
  isInternationalLocationLoading: boolean = false;
  isAgencyNameListLoading: boolean = true;
  isAreaUnitListLoading: boolean = true;
  isActiveDataLoading: boolean = false;
  enquiredFor: string[] = [];
  bhkTypesSelected: string[] = [];
  bhkNoSelected: string[] = [];
  manualLocationsList: any[] = [];
  backupLocation: any[] = [];
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  isPostingData: boolean;
  campaignList: Array<any>;
  campaignListIsLoading: boolean;
  fieldSections: any = [
    {
      name: 'Data Info',
      isHide: false,
      icon: 'ic-notes-pen',
    },
    {
      name: 'Enquiry Info',
      isHide: false,
      icon: 'ic-circle-exclamation-solid',
    },
    {
      name: 'Additional Info',
      isHide: false,
      icon: 'ic-file-clip',
    },
    {
      name: 'Others',
      isHide: false,
      icon: 'ic-hexagon-arrow',
    },
    {
      name: 'Notes',
      isHide: false,
      icon: 'ic-notes-solid',
    },
  ];
  scrollpause: boolean = false;
  currentActive: any = 0;
  receivedCurrentPath: string;
  isBackSpace: boolean;
  globalSettingsDetails: any;

  isCreateDuplicateData: boolean = false;
  isDuplicateData: boolean = false;
  nationalities: any[] = [];
  isOpenPossessionModal: boolean = false;
  selectedPossession: any;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  selectedMonthAndYear: Date;
  isValidPossDate: boolean;
  selectedMonth: any;
  selectedYear: any;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  // New Possession Needed By properties
  isOpenPossessionNeededByModal: boolean = false;
  selectedPossessionNeededBy: string = '';
  selectedPossessionNeededByOption: string = '';
  possessionNeededByList = POSSESSION_NEEDED_BY_LIST;
  isValidPossessionNeededByDate: boolean = false;
  @ViewChild('dtPossessionNeededBy') dtPossessionNeededBy: OwlDateTimeComponent<any>;
  @ViewChild('dt2') dt2: OwlDateTimeComponent<any>;
  genders: any[] = GENDER;
  maritalStatus: any[] = MARITAL_STATUS;
  possessionType: any = PossessionType;

  isSourcesLoading: boolean;
  allowLandlineInput = allowLandlineInput;

  get minDate(): Date {
    const minDate = new Date(this.currentDate);
    minDate.setHours(0, 0, 0, 0);
    return minDate;
  }

  get maxDate(): Date {
    const maxDate = new Date(this.currentDate);
    maxDate.setHours(0, 0, 0, 0);
    return maxDate;
  }

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private formBuilder: FormBuilder,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    public trackingService: TrackingService,
    public modalRef: BsModalRef,
  ) { }

  ngOnInit(): void {
    this.nationalities = Object.values(countries).map((country: any) => ({ name: country.name }));
    this.trackingService.trackFeature(`Web.Data.Button.AddData.View`);

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.isCustomLeadFormEnabled) {
          this._store.dispatch(new FetchInternationalLocations());
        }

        this.globalSettingsDetails = data;
        this.currency = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        (this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null),
          (this.hasInternationalSupport = data?.hasInternationalSupport);
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.timeZone = data?.defaultTimeZone;
        this.addDataForm?.patchValue({
          currency:
            this.selectedDataInfo?.enquiry?.currency || this.defaultCurrency,
        });
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGlobalSettingsLoading = isLoading;
      });

    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => {
          if (this.isBackSpace) {
            return true;
          } else {
            return searchStr?.length > 2;
          }
        })
      )
      .subscribe((searchStr: string) => {
        if (this.globalSettingsDetails?.isCustomLeadFormEnabled) {
          this._store.dispatch(new FetchInternationalLocations(searchStr));
        } else {
          this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
        }
      });

    this.addDataForm = this.formBuilder.group({
      name: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      contactNo: ['', this.contactNumberValidator('primary')],
      alternateContactNo: ['', this.contactNumberValidator('alternative')],
      landLine: ['', ValidationUtil.landlineNumberValidator],
      email: ['', ValidationUtil.emailValidatorMinLength],
      dataSource: [null],
      subSource: [null],
      lowerBudget: [null],
      currency: [this.defaultCurrency],
      upperBudget: [null],
      assignTo: JSON.parse(localStorage.getItem('userDetails'))?.sub,
      assignedFrom: [null],
      enquiredFor: null,
      propertyTypeId: [null],
      propSubType: [null],
      noOfBHK: [null],
      bhkType: [null],
      beds: [null],
      baths: [null],
      preferredFloors: [null],
      notes: '',
      projectsList: [null],
      propertiesList: [null],
      agencies: [null],
      companyName: [null],
      designation: [null],
      possessionDate: [null],
      carpetArea: [null],
      carpetAreaUnitId: [null],
      builtUpArea: [null],
      builtUpAreaUnitId: [null],
      saleableArea: [null],
      saleableAreaUnitId: [null],
      propertyArea: [null],
      propertyAreaUnitId: [null],
      netArea: [null],
      netAreaUnitId: [null],
      unitName: [null],
      clusterName: [null],
      nationality: [null],
      closingManager: [null],
      sourcingManager: [null],
      profession: null,
      customerLocationId: [null],
      customerLocality: null,
      customerSubCommunity: null,
      customerCommunity: null,
      customerTowerName: null,
      customerCity: null,
      customerState: null,
      customerCountry: null,
      customerPincode: null,
      channelPartnerList: [null],
      campaigns: [null],
      enquiredLocationId: [null],
      enquiredLocality: null,
      enquiredSubCommunity: null,
      enquiredCommunity: null,
      enquiredTowerName: null,
      enquiredCity: null,
      enquiredState: null,
      enquiredCountry: null,
      enquiredPincode: null,
      referralName: [''],
      referralContactNo: ['', this.contactNumberValidator('referral')],
      referralEmail: ['', ValidationUtil.emailValidatorMinLength],
      offeringType: [null],
      furnishStatus: [null],
      purpose: [null],
      possessionRange: [null],
      customPossessionDate: [null],
      possessionNeededBy: [null],
      customPossessionNeededByDate: [null],
      gender: [null],
      maritalStatus: [null],
      dateOfBirth: [null],
    });

    this._store.dispatch(new FetchDataSourceList());
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchPropertyList());
    this._store.dispatch(new FetchChannelPartnerList());
    this._store.dispatch(new FetchAdminsAndReportees());
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store.dispatch(new FetchCampaignList());

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });

    this._store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyListLoading = isLoading;
      });

    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAgencyNameListLoading = isLoading;
      });

    this._store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.campaignList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.campaignListIsLoading = isLoading;
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAreaUnitListLoading = isLoading;
      });

    this._store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.channelPartnerList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.channelPartnerListIsLoading = isLoading;
      });

    this._store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.campaignList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.campaignListIsLoading = isLoading;
      });

    this._store.dispatch(new FetchAllSources());
    const allSources$ = this._store.select(getAllSources);
    const dataSourceList$ = this._store.select(getDataSourceList);
    combineLatest([allSources$, dataSourceList$])
      .pipe(takeUntil(this.stopper))
      .subscribe(([leadSource, dataSource]) => {
        if (leadSource && dataSource) {
          const matched = dataSource
            .filter((data: any) =>
              leadSource.some((lead: any) => lead?.value === data?.value && lead?.isEnabled))
            .map((data: any) => {
              const matchedLead = leadSource.find((lead: any) => lead?.value === data?.value);
              return {
                ...data,
                isEnabled: matchedLead?.isEnabled ?? false,
                isDefault: matchedLead?.isDefault ?? false
              };
            });
          const sorted = matched.sort((a: any, b: any) => {
            if (a.isEnabled === b.isEnabled) {
              return a.displayName.localeCompare(b.displayName);
            }
            return a.isEnabled ? -1 : 1;
          });
          this.sourceList = sorted;
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this._store
      .select(getDataSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.subSourceList = item;
        this.updateSubSources(null);
      });

    this._store
      .select(getDataSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isSubSourceListLoading = isLoading;
      });

    this._store
      .select(getActiveData)
      .pipe(
        takeUntil(this.stopper),
        filter((data: any) => data.id === this.selectedDataId)
      )
      .subscribe((data: any) => {
        this.selectedDataInfo = data;
        if (!this.selectedDataInfo?.alternateContactNo) {
          const input = document.querySelector(
            '.alternateNoInput > div > input'
          ) as HTMLInputElement;
          if (input) input.value = null;
        }
        if (Object.keys(data).length) {
          this.addDataForm.patchValue({
            ...this.selectedDataInfo,
            name: data?.name,
            contactNo: data?.contactNo,
            alternateContactNo: data?.alternateContactNo,
            email: data?.email,
            referralName: data?.referralName,
            referralEmail: data?.referralEmail,
            referralContactNo: data?.referralContactNo,
            notes: data?.notes,
            assignTo: data?.assignTo !== EMPTY_GUID ? data?.assignTo : null,
            assignedFrom:
              data?.assignedFrom !== EMPTY_GUID ? data?.assignedFrom : null,
            agencies: data?.agencies?.map((agency: any) => agency?.name),
            companyName: data?.companyName,
            designation: data?.designation,
            possessionDate: patchTimeZoneDate(
              data?.possesionDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            profession: data?.profession ? Profession[data?.profession] : null,
            closingManager:
              data?.closingManager !== EMPTY_GUID ? data?.closingManager : null,
            sourcingManager:
              data?.sourcingManager !== EMPTY_GUID
                ? data?.sourcingManager
                : null,
            channelPartnerList: data?.channelPartners?.map(
              (channelPartner: any) => channelPartner?.firmName
            ),
            campaigns: data?.campaigns?.map(
              (campaigns: any) => campaigns?.name || null,
            ),
            lowerBudget: data?.enquiry?.lowerBudget || null,
            upperBudget: data?.enquiry?.upperBudget || null,
            currency: data?.enquiry?.currency || this.defaultCurrency,
            unitName: data?.enquiry?.unitName,
            nationality: data?.nationality,
            clusterName: data?.enquiry?.clusterName,
            carpetArea: data?.enquiry?.carpetArea || null,
            carpetAreaUnitId:
              data?.enquiry?.carpetAreaUnitId && data.enquiry?.carpetArea
                ? data.enquiry?.carpetAreaUnitId
                : null,
            builtUpArea: data.enquiry?.builtUpArea || null,
            builtUpAreaUnitId:
              data.enquiry?.builtUpAreaUnitId && data.enquiry?.builtUpArea
                ? data.enquiry?.builtUpAreaUnitId
                : null,

            saleableArea: data.enquiry?.saleableArea || null,
            saleableAreaUnitId:
              data.enquiry?.saleableAreaUnitId && data.enquiry?.saleableArea
                ? data.enquiry?.saleableAreaUnitId
                : null,
            propertyArea: data?.enquiry?.propertyArea || null,
            propertyAreaUnitId:
              data?.enquiry?.propertyAreaUnitId && data.enquiry?.propertyArea
                ? data.enquiry?.propertyAreaUnitId
                : null,
            netArea: data?.enquiry?.netArea || null,
            netAreaUnitId:
              data?.enquiry?.netAreaUnitId && data.enquiry?.netArea
                ? data.enquiry?.netAreaUnitId
                : null,
            enquiredFor:
              data?.enquiry?.enquiryTypes?.map(
                (eType: any) => EnquiryType[eType]
              ) || [],
            propertyTypeId: data?.enquiry?.propertyTypes?.[0]?.displayName,
            propSubType: data?.enquiry?.propertyTypes?.map((item: any) => item?.childType?.displayName),
            noOfBHK: this.globalSettingsDetails?.isCustomLeadFormEnabled
              ? data?.enquiry?.bhKs
              : data?.enquiry?.bhKs?.map((bhk: any) => bhk?.toString()),
            bhkType:
              data?.enquiry?.bhkTypes?.map((type: any) => BHKType[type]) || [],
            baths: data?.enquiry?.baths,
            beds: data?.enquiry?.beds,
            offeringType: data?.enquiry?.offerType
              ? data.enquiry?.offerType
              : null,
            purpose: data?.enquiry?.purpose
              ? data.enquiry?.purpose
              : null,
            furnishStatus: data?.enquiry?.furnished
              ? FurnishStatus[data?.enquiry?.furnished]
              : null,
            preferredFloors: data?.enquiry?.floors,
            propertiesList: data?.properties?.map(
              (property: any) => property?.title
            ),
            projectsList: data?.projects?.map((project: any) => project?.name),
            dataSource: this.sourceList?.find((source: any) => source.id === data?.enquiry?.prospectSource?.id) || data?.enquiry?.prospectSource?.id,
            subSource: this.selectedDataInfo?.enquiry?.subSource || null,
            enquiredLocationId:
              getLocationDetailsByObj(
                this.selectedDataInfo?.enquiry?.addressDto
              ) || null,
            enquiredLocality:
              getLocalityDetailsByObj(
                this.selectedDataInfo?.enquiry?.addressDto
              ) || null,
            customerLocationId:
              getLocationDetailsByObj(this.selectedDataInfo?.addressDto) ||
              null,
            customerLocality:
              getLocalityDetailsByObj(this.selectedDataInfo?.addressDto) ||
              null,
            customerCity: this.selectedDataInfo.addressDto?.city,
            customerState: this.selectedDataInfo.addressDto?.state,
            customerCountry: this.selectedDataInfo.addressDto?.country,
            customerSubCommunity:
              this.selectedDataInfo.addressDto?.subCommunity,
            customerCommunity: this.selectedDataInfo?.addressDto?.community,
            customerTowerName: this.selectedDataInfo?.addressDto?.towerName,
            customerPincode: this.selectedDataInfo.addressDto?.postalCode,
            enquiredCity: this.selectedDataInfo.enquiry?.addressDto?.city,
            enquiredState: this.selectedDataInfo.enquiry?.addressDto?.state,
            enquiredCountry: this.selectedDataInfo.enquiry?.addressDto?.country,
            enquiredSubCommunity:
              this.selectedDataInfo.enquiry?.addressDto?.subCommunity,
            enquiredCommunity:
              this.selectedDataInfo.enquiry?.addressDto?.community,
            enquiredTowerName:
              this.selectedDataInfo.enquiry?.addressDto?.towerName,
            enquiredPincode: this.selectedDataInfo.enquiry?.address?.postalCode,
            possessionRange:
              this.selectedDataInfo?.enquiry.possesionType === 0
                ? null
                : PossessionType[this.selectedDataInfo?.enquiry?.possesionType],
            customPossessionDate: this.selectedDataInfo?.possesionDate,
            gender: this.selectedDataInfo?.gender ? this.selectedDataInfo?.gender : null,
            maritalStatus: this.selectedDataInfo?.maritalStatus ? this.selectedDataInfo?.maritalStatus : null,
            dateOfBirth: this.selectedDataInfo?.dateOfBirth ? getTimeZoneDate(this.selectedDataInfo?.dateOfBirth, '00:00:00', 'ISO') : null,
          });
          this.patchDefaultUnits()
          this.enquiredFor =
            this.selectedDataInfo?.enquiry?.enquiryTypes?.map(
              (eType: any) => EnquiryType[eType]
            ) || [];
          this.bhkNoSelected =
            this.selectedDataInfo?.enquiry?.bhKs?.map((bhkNo: any) =>
              bhkNo.toString()
            ) || [];
          if (
            this.selectedDataInfo?.enquiry?.bhKs?.find((bhk: any) => bhk >= 5.5)
          ) {
            this.bhkNoList = BHK_NO_ALL;
          }
          this.manualLocationsList =
            this.selectedDataInfo?.enquiry?.addresses?.map((address: any) => {
              let adr = {
                ...address,
                locationId: address?.locationId ?? address?.locationId,
                placeId: address?.placeId ?? address?.placeId,
                locality: address?.locality || address?.subLocality,
                postalCode: address?.postalCode ?? address?.postalCode,
                id: address?.id ?? address?.id,
              };
              return adr;
            });

          this.backupLocation = this.selectedDataInfo?.enquiry?.addresses;
          this.bhkTypesSelected =
            this.selectedDataInfo?.enquiry?.bhkTypes?.map(
              (bhkType: any) => BHKType[bhkType]
            ) || [];
        }
      });

    this._store
      .select(getActiveDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isActiveDataLoading = this.router.url?.includes('add')
          ? false
          : isLoading;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsWithReportees, allUsers, permissions }) => {
      this.allUsers = allUsers?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
          label: `${user.firstName} ${user.lastName}`,
        };
        return user;
      });

      if (permissions?.includes('Permissions.Prospects.Create'))
        if (
          permissions?.includes(
            'Permissions.Prospects.CreateDuplicateProspects'
          )
        ) {
          this.isCreateDuplicateData = true;
        }
      this.canViewComponent = true;
      if (permissions?.includes('Permissions.Prospects.Update'))
        this.canEdit = true;
      if (this.canEdit && this.router.url?.includes('edit'))
        this.canViewComponent = true;
      if (permissions?.includes('Permissions.Prospects.Assign'))
        this.canAssign = true;
      if (permissions?.includes('Permissions.Prospects.UpdateSource'))
        this.canAdminEditSource = true;
      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
        this.isUsersLoading = false;
      } else {
        this.users = adminsWithReportees;
        this.isUsersLoading = false;
      }

      this.activeUsers = this.users?.filter((user: any) => user.isActive);
      this.inactiveUsers = this.users?.filter((user: any) => !user.isActive);
      this.users = assignToSort(
        this.activeUsers,
        this.selectedDataInfo?.assignTo
      );
    });

    this.addDataForm.controls['possessionRange'].valueChanges.subscribe(
      (value) => {
        if (value === 'Custom Date') {
          this.convertDateToMonth(
            this.addDataForm.get('customPossessionDate').value ?? this.selectedDataInfo?.possesionDate
          );
        } else if (
          value === '6 Months' ||
          value === '1 Year' ||
          value === '2 Years' ||
          value === 'Under Construction'
        ) {
          this.calculateDateRange(value);
          this.selectedPossession = null;
          this.isValidPossDate = false;
        }
      }
    );

    this.addDataForm.get('currency').valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.addDataForm.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.addDataForm.controls['contactNo'].valueChanges.subscribe(
      (val: string = '') => {
        if (this.selectedDataId && val !== this.selectedDataInfo?.contactNo) {
          this.numberEdited = true;
        } else if (this.selectedDataId) {
          this.numberEdited = false;
        }
        if (val?.length && this.contactNoInput && val !== this.selectedDataInfo?.contactNo) {
          const input = document.querySelector(
            '.contactNoInput > div > input'
          ) as HTMLInputElement;
          if (!this.contactNoInput?.phoneNumber) {
            return
          }
          this._store.dispatch(
            new FetchDataIdWithContactNo({
              value: val,
              number: +input?.value?.replace(' ', '') || '',
              countryCode:
                '+' + this.getSelectedCountryCodeContactNo()?.dialCode,
            })
          );
          this._store.dispatch(new LoaderHide());
          this._store
            .select((state: any) => state?.dataManagement)
            .pipe(takeUntil(this.stopper))
            .subscribe((state: any) => {
              const data = state?.duplicateDataId;
              const isLoading = state?.isLoadingHasDataInfo;
              if (data?.id && data?.id !== this.selectedDataId && !isLoading) {
                this.isDuplicateData = true;
                if (this.isCreateDuplicateData) {
                  this.checkDuplicacy = false;
                } else {
                  this.checkDuplicacy = true;
                }
                this.duplicateDataId = data?.id;
                this.canNavigate = data?.canNavigate;
                if (data?.id)
                  this.numberEdited = data?.id !== this.selectedDataId;
              } else {
                this.isDuplicateData = false;
                this.checkDuplicacy = false;
              }
            });
        } else {
          this.numberEdited = false;
          this.checkDuplicacy = false;
        }
      }
    );

    this.addDataForm.controls['alternateContactNo'].valueChanges.subscribe(
      (val: string) => {
        if (
          this.selectedDataId &&
          val !== this.selectedDataInfo?.alternateContactNo
        ) {
          this.alternateNumberEdited = true;
        } else if (this.selectedDataId) {
          this.alternateNumberEdited = false;
        }
        if (val?.length && this.alternateNoInput && val !== this.selectedDataInfo?.alternateContactNo) {
          const input = document.querySelector(
            '.alternateNoInput > div > input'
          ) as HTMLInputElement;
          if (!this.alternateNoInput?.phoneNumber) {
            return
          }
          this._store.dispatch(
            new FetchDataIdWithAltNo({
              value: val,
              number: +input?.value?.replace(' ', '') || '',
              countryCode:
                '+' + this.getSelectedCountryCodeAlternateNo()?.dialCode,
            })
          );
          this._store.dispatch(new LoaderHide());
          this._store
            .select((state: any) => state?.dataManagement)
            .pipe(takeUntil(this.stopper))
            .subscribe((state: any) => {
              const data = state?.duplicateDataAltId;
              const isLoading = state?.isLoadingHasDataAltInfo;
              if (data?.id && data?.id !== this.selectedDataId && !isLoading) {
                this.isDuplicateData = true;
                if (this.isCreateDuplicateData) {
                  this.checkAlternateNumDuplicacy = false;
                } else {
                  this.checkAlternateNumDuplicacy = true;
                }
                this.duplicateDataAltId = data?.id;
                this.canAltNavigate = data?.canNavigate;
                if (data?.id)
                  this.alternateNumberEdited = data?.id !== this.selectedDataId;
              } else {
                this.isDuplicateData = false;
                this.checkAlternateNumDuplicacy = false;
              }
            });
        } else {
          this.isDuplicateData = false;
          this.checkAlternateNumDuplicacy = false;
        }
      }
    );

    this.addDataForm.get('carpetArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(VALIDATION_SET, this.addDataForm, 'carpetAreaUnitId', [
          Validators.required,
        ]);
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addDataForm,
          'carpetAreaUnitId'
        );
        this.addDataForm.patchValue({
          carpetAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addDataForm
      .get('carpetAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.carpetAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addDataForm.get('builtUpArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addDataForm,
          'builtUpAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addDataForm,
          'builtUpAreaUnitId'
        );
        this.addDataForm.patchValue({
          builtUpAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addDataForm
      .get('builtUpAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.builtUpAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });

    this.addDataForm.get('saleableArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addDataForm,
          'saleableAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addDataForm,
          'saleableAreaUnitId'
        );
        this.addDataForm.patchValue({
          saleableAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addDataForm
      .get('saleableAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.saleableAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });


    this.addDataForm.get('propertyArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addDataForm,
          'propertyAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addDataForm,
          'propertyAreaUnitId'
        );
        this.addDataForm.patchValue({
          propertyAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addDataForm
      .get('propertyAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.propertyAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });


    this.addDataForm.get('netArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addDataForm,
          'netAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addDataForm,
          'netAreaUnitId'
        );
        this.addDataForm.patchValue({
          netAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addDataForm
      .get('netAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.netAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });

    this.addDataForm.controls['propertyTypeId'].valueChanges.subscribe(
      (val: string) => {
        this.propSubTypes = setPropertySubTypeList(val, this.propertyTypeList);
      }
    );

    this.addDataForm
      .get('propertyTypeId')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(VALIDATION_SET, this.addDataForm, 'propSubType', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.addDataForm, 'propSubType');
        }
        this.addDataForm.patchValue({
          propSubType: null,
        });
      });

    this.addDataForm.get('currency').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        this.selectedDataInfo?.enquiry?.lowerBudget ||
        this.addDataForm.value.lowerBudget,
        val
      );
      this.upperBudgetInWords = formatBudget(
        this.selectedDataInfo?.enquiry?.upperBudget ||
        this.addDataForm.value.upperBudget,
        val
      );
    });

    this.addDataForm.get('lowerBudget').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        val,
        this.addDataForm.value.currency ||
        this.selectedDataInfo?.enquiry?.currency ||
        this.defaultCurrency
      );
      if (!this.addDataForm.value.upperBudget) {
        this.addDataForm.value.upperBudget = val;
      } else {
        val > this.addDataForm.get('upperBudget').value
          ? (this.budgetValidation = true)
          : (this.budgetValidation = false);
      }
    });

    this.addDataForm.get('upperBudget').valueChanges.subscribe((val) => {
      this.upperBudgetInWords = formatBudget(
        val,
        this.addDataForm.value.currency ||
        this.selectedDataInfo?.enquiry?.currency ||
        this.defaultCurrency
      );
      this.addDataForm.get('lowerBudget').value > val
        ? (this.budgetValidation = true)
        : (this.budgetValidation = false);
    });

    this.addDataForm.valueChanges.subscribe((data: any) => {
      data.enquiryTypes = this.enquiredFor;
      data.bhkNo = this.bhkNoSelected;
      data.bhkTypes = this.bhkTypesSelected;
      this.currFormValues = data;
    });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          ?.map(({ id, ...location }: any) => ({ locationId: id, ...location }))
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.onUnitChange('carpetAreaUnitId');

    /* Fetching Places List */
    this._store
      .select(getInternationalLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.internationalLocations = data
          ?.slice()
          // ?.map(({ id, ...location }: any) => ({ locationId: id, ...location }))
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this._store
      .select(getInternationLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isInternationalLocationLoading = isLoading;
      });
    this.patchDefaultUnits()
  }

  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.addDataForm.value.enquiredLocationId = null;
        this.addDataForm.value.enquiredLocality = null;
        this.addDataForm.value.enquiredCity = null;
        this.addDataForm.value.enquiredState = null;
        this.addDataForm.value.enquiredCountry = null;
        this.addDataForm.value.enquiredSubCommunity = null;
        this.addDataForm.value.enquiredCommunity = null;
        this.addDataForm.value.enquiredTowerName = null;
        this.addDataForm.value.enquiredPincode = null;
        break;
      case 'changeLocation':
        [
          'enquiredLocality',
          'enquiredCity',
          'enquiredState',
          'enquiredCountry',
          'enquiredSubCommunity',
          'enquiredCommunity',
          'enquiredTowerName',
          'enquiredPincode',
        ].forEach((control) => {
          this.addDataForm.get(control).reset();
          this.addDataForm.markAsDirty();
        });
        break;
      case 'changeLocality':
        this.addDataForm.patchValue({
          enquiredLocationId: null,
        });
        break;
    }
  }

  ngAfterViewInit(): void {
    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedDataId = params.id;
        this._store.dispatch(new FetchDataById(this.selectedDataId));
      }

      this.headerTitle.setLangTitle(
        this.selectedDataId ? 'BUTTONS.edit-data' : 'BUTTONS.add-data'
      );
    });
  }

  handleCheckboxClick(event: MouseEvent, value: string, type: string): void {
    if (type == 'bhkNo' && value === '5+') {
      return;
    }
    if (type == 'bhkType')
      this.bhkTypesSelected = this.toggleCheckboxSelection(
        this.bhkTypesSelected,
        value,
        event
      );
    else if (type == 'bhkNo')
      this.bhkNoSelected = this.toggleCheckboxSelection(
        this.bhkNoSelected,
        value,
        event
      );
    else
      this.enquiredFor = this.toggleCheckboxSelection(
        this.enquiredFor,
        value,
        event
      );
    this.currFormValues = {
      ...this.currFormValues,
      enquiryTypes: this.enquiredFor,
      bhkNo: this.bhkNoSelected,
      bhkTypes: this.bhkTypesSelected,
    };
  }

  toggleCheckboxSelection(array: string[], value: string, event: MouseEvent) {
    if (array?.includes(value))
      array = array?.filter((enquiry: string) => enquiry !== value);
    else if (!array?.includes(value)) array.push(value);
    else event?.preventDefault();
    return array;
  }

  clearManualLocation(location: any) {
    this.manualLocationsList = this.manualLocationsList.filter(
      (manualLocation: any) =>
        JSON.stringify(manualLocation) != JSON.stringify(location)
    );
    this.addDataForm?.markAsDirty();
  }

  addMoreLocation() {
    const {
      enquiredCity,
      enquiredLocality,
      enquiredState,
      enquiredCountry,
      enquiredSubCommunity,
      enquiredCommunity,
      enquiredTowerName,
      enquiredPincode
    }: any = this.addDataForm.value;
    if (
      !enquiredCity?.trim() &&
      !enquiredState?.trim() &&
      !enquiredLocality?.trim() &&
      !enquiredCountry?.trim() &&
      !enquiredSubCommunity?.trim() &&
      !enquiredCommunity?.trim() &&
      !enquiredTowerName?.trim() &&
      !enquiredPincode?.trim()
    ) {
      return;
    }
    const filteredLocation = this.manualLocationsList.filter(
      (location: any) => {
        return (
          enquiredCity?.trim() == location?.city &&
          enquiredLocality?.trim() == location?.locality &&
          enquiredState?.trim() == location?.state &&
          enquiredCountry?.trim() == location?.country &&
          enquiredSubCommunity?.trim() == location?.subCommunity &&
          enquiredCommunity?.trim() == location?.community &&
          enquiredTowerName?.trim() == location?.towerName &&
          enquiredPincode?.trim() == location?.postalCode
        );
      }
    );
    if (filteredLocation?.length) return;
    this.manualLocationsList.push({
      city: enquiredCity?.trim(),
      locality: enquiredLocality?.trim(),
      state: enquiredState?.trim(),
      country: enquiredCountry?.trim(),
      subCommunity: enquiredSubCommunity?.trim(),
      community: enquiredCommunity?.trim(),
      towerName: enquiredTowerName?.trim(),
      postalCode: enquiredPincode?.trim(),
    });
    this.removeLocation('changeLocation');
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.alternateNoInput?.selectedCountry;
  }

  getSelectedCountryCodeReferralNo(): any {
    return this.referralNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const primaryNumber = this.addDataForm?.get('contactNo')?.value;
      const alternativeNumber =
        this.addDataForm?.get('alternateContactNo')?.value;
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
        if (control.value && alternativeNumber) {
          if (control.value === alternativeNumber) {
            this.addDataForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: true });
            this.addDataForm.get('alternateContactNo')?.markAsTouched();
          } else {
            this.addDataForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: false });
            this.addDataForm.get('alternateContactNo')?.markAsPristine();
            this.addDataForm.get('alternateContactNo')?.markAsUntouched();
          }
          this.addDataForm.get('alternateContactNo')?.updateValueAndValidity();
          return null;
        }
      } else if (numType == 'alternative') {
        const input = document.querySelector(
          '.alternateNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;
        if (control.value && primaryNumber && control.value === primaryNumber) {
          return { samePhoneNumber: true };
        }
      } else if (numType == 'referral') {
        const input = document.querySelector(
          '.referralNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeReferralNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          (numType == 'primary'
            ? this.contactNoInput?.value
            : numType == 'alternative'
              ? this.alternateNoInput?.value
              : this.referralNoInput?.value) || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  appendToBHKList(bhkNo: string) {
    if (bhkNo != '5+') return;
    this.bhkNoList = BHK_NO_ALL;
  }

  postData() {
    if (
      this.addDataForm.value.lowerBudget &&
      !this.addDataForm.value.upperBudget
    ) {
      this.addDataForm.value.upperBudget = this.addDataForm.value.lowerBudget;
    } else if (
      !this.addDataForm.value.lowerBudget &&
      this.addDataForm.value.upperBudget
    ) {
      this.addDataForm.value.lowerBudget = this.addDataForm.value.upperBudget;
    }
    if (
      !this.addDataForm.valid ||
      (this.checkDuplicacy && this.numberEdited) ||
      (this.checkAlternateNumDuplicacy && this.alternateNumberEdited) ||
      this.addDataForm.value.lowerBudget > this.addDataForm.value.upperBudget
    ) {
      validateAllFormFields(this.addDataForm);
      return;
    }
    const payload = this.preparePayload();
    if (!this.selectedDataId) {
      this.isPostingData = true;
      this._store.dispatch(new AddNewData(payload));
      this._store
        .select(getDataIsAdding)
        .pipe(skipWhile((isLoading) => isLoading))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isPostingData = isLoading;
            this.navigateToManageData();
          }
        });
    } else {
      this.isPostingData = true;
      this._store.dispatch(new UpdateData(this.selectedDataId, payload));
      this._store
        .select(getDataIsUpdating)
        .pipe(skipWhile((isLoading) => isLoading))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isPostingData = isLoading;
            this.navigateToManageData();
          }
        });
    }
    this.trackingService.trackFeature('Web.Data.Button.SaveData.Click')
    this._store.dispatch(new ClearData());
  }

  preparePayload(): any {
    const data = this.addDataForm.value;
    if (!data.customerLocationId && !this.isShowManualCustomerLocation) {
      data.customerLocality = null;
      data.customerSubCommunity = null;
      data.customerCommunity = null;
      data.customerTowerName = null;
      data.customerCity = null;
      data.customerState = null;
      data.customerCountry = null;
    }
    let payload: any = {
      name: data?.name,
      contactNo: data?.contactNo,
      alternateContactNo: data?.alternateContactNo,
      landLine: data?.landLine,
      countryCode: data?.contactNo ? this.getSelectedCountryCodeContactNo()?.dialCode : null,
      altCountryCode: data?.alternateContactNo ? this.getSelectedCountryCodeAlternateNo()?.dialCode : null,
      email: data?.email,
      notes: data?.notes ? data?.notes : null,
      assignTo: data?.assignTo,
      assignedFrom: data?.assignedFrom,
      agencies: data?.agencies?.map((name: any) => ({ name })),
      companyName: data?.companyName,
      designation: data?.designation,
      possesionDate:
        data?.possessionRange === 'Under Construction'
          ? null
          : data?.possessionRange === 'Custom Date' ||
            data?.possessionRange === '6 Months' ||
            data?.possessionRange === '1 Year' ||
            data?.possessionRange === '2 Years'
            ? data?.customPossessionDate
              ? setTimeZoneDate(
                data.customPossessionDate,
                this.userData?.timeZoneInfo?.baseUTcOffset
              )
              : null
            : this.selectedDataId && this.selectedDataInfo?.enquiry?.possessionDate
              ? this.selectedDataInfo?.enquiry?.possessionDate
              : null,
      profession: data?.profession ? Profession[data?.profession] : 0,
      closingManager: data?.closingManager,
      sourcingManager: data?.sourcingManager,
      addressDto: {
        locationId:
          data.customerLocationId?.locationId ??
          data.customerLocationId?.locationId,
        placeId:
          data.customerLocationId?.placeId ?? data.customerLocationId?.placeId,
        subLocality: data.customerLocality ?? data.customerLocality,
        subCommunity: data.customerSubCommunity ?? data.customerSubCommunity,
        community: data.customerCommunity ?? data.customerCommunity,
        towerName: data.customerTowerName ?? data.customerTowerName,
        city: data.customerCity ?? data.customerCity,
        state: data.customerState ?? data.customerState,
        country: data.customerCountry ?? data.customerCountry,
        postalCode: data.customerPincode ?? data.customerPincode,
      },
      channelPartnerList: data?.channelPartnerList,
      campaigns: data?.campaigns?.map((name: any) => ({ name })),
      nationality: data?.nationality,
      enquiry: {
        bhkTypes:
          this.addDataForm.get('propertyTypeId').value === 'Residential' &&
            data.propSubType != 'Plot'
            ? data.bhkType?.map((bhkType: any) => BHKType[bhkType])
            : [],
        bhKs:
          this.addDataForm.get('propertyTypeId').value === 'Residential' &&
            data.propSubType !== 'Plot'
            ? (data?.noOfBHK || [])
              .filter((bhkNo: any) => bhkNo != null && parseFloat(bhkNo) > 0)
              ?.map((bhkNo: any) => parseFloat(bhkNo))
              .sort((a: number, b: number) => a - b)
            : [],
        beds:
          this.addDataForm.get('propertyTypeId').value === 'Residential' &&
            data.propSubType !== 'Plot'
            ? data?.beds
              ?.filter((bed: any) => bed != null)
              .slice()
              .sort((a: any, b: any) => String(a).localeCompare(String(b)))
            : [],
        baths:
          this.addDataForm.get('propertyTypeId').value === 'Residential' &&
            data.propSubType !== 'Plot'
            ? data?.baths
              ?.filter((bed: any) => bed != null)
              .slice()
              .sort((a: any, b: any) => String(a).localeCompare(String(b)))
            : [],
        furnished: data.furnishStatus
          ? FurnishStatus[data.furnishStatus]
          : null,
        floors: data.preferredFloors ?? null,
        offerType: data.offeringType ?? null,
        purpose: data.purpose ?? null,
        addresses: (
          [
            ...(data?.enquiredLocationId || []),
            ...(this.manualLocationsList || []),
            ...(data.enquiredCity?.trim() ||
              data?.enquiredLocality?.trim() ||
              data?.enquiredSubCommunity?.trim() ||
              data?.enquiredCommunity?.trim() ||
              data?.enquiredTowerName?.trim() ||
              data?.enquiredState?.trim() ||
              data?.enquiredCountry?.trim()
              ? [
                {
                  enquiredLocality:
                    data.enquiredLocality ?? data.enquiredLocality,
                  enquiredSubCommunity:
                    data.enquiredSubCommunity ?? data.enquiredSubCommunity,
                  enquiredCommunity:
                    data.enquiredCommunity ?? data.enquiredCommunity,
                  enquiredTowerName:
                    data.enquiredTowerName ?? data.enquiredTowerName,
                  enquiredCity: data.enquiredCity ?? data.enquiredCity,
                  enquiredState: data.enquiredState ?? data.enquiredState,
                  enquiredCountry:
                    data.enquiredCountry ?? data.enquiredCountry,
                },
              ]
              : []),
          ] || this.backupLocation
        )?.map((location: any) => {
          let locationPayload = {
            locationId: location?.locationId ?? location?.locationId,
            placeId: location?.placeId ?? location?.placeId,
            subLocality:
              (location?.enquiredLocality ?? location?.enquiredLocality) ||
              (location?.locality ?? location?.locality),
            city:
              (location?.enquiredCity ?? location?.enquiredCity) ||
              (location?.city ?? location?.city),
            subCommunity:
              (location?.enquiredSubCommunity ??
                location?.enquiredSubCommunity) ||
              (location?.subCommunity ?? location?.subCommunity),
            community:
              (location?.enquiredCommunity ?? location?.enquiredCommunity) ||
              (location?.community ?? location?.community),
            towerName:
              (location?.enquiredTowerName ?? location?.enquiredTowerName) ||
              (location?.towerName ?? location?.towerName),
            state:
              (location?.enquiredState ?? location?.enquiredState) ||
              (location?.state ?? location?.state),
            country:
              (location?.enquiredCountry ?? location?.enquiredCountry) ||
              (location?.country ?? location?.country),
            postalCode: location?.postalCode ?? location?.postalCode
          };

          if (
            this.globalSettingsDetails?.isCustomLeadFormEnabled &&
            !location.placeId &&
            typeof location.location === 'string' &&
            location.location.trim()
          ) {
            const [
              towerName = '',
              subCommunity = '',
              community = '',
              city = '',
            ] = location.location.split(',').map((name: any) => name.trim());
            return {
              ...locationPayload,
              towerName,
              subCommunity,
              community,
              city,
            };
          }
          return locationPayload;
        }),
        enquiryTypes:
          data.enquiredFor?.map((enquiry: any) => EnquiryType[enquiry]) || [],
        subSource: data?.subSource,
        lowerBudget: data?.lowerBudget ? data?.lowerBudget : null,
        upperBudget: data?.upperBudget ? data?.upperBudget : null,
        currency: data?.currency ? data?.currency : this.defaultCurrency,
        possessionDate: setTimeZoneDate(
          data?.possessionDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        carpetArea: data?.carpetArea || null,
        carpetAreaUnitId: data?.carpetArea && data?.carpetAreaUnitId
          ? data.carpetAreaUnitId
          : null,
        conversionFactor:
          data?.carpetArea && data?.carpetAreaUnitId
            ? Number(this.carpetAreaConversion)
            : null,
        builtUpArea: data.builtUpArea || null,
        builtUpAreaUnitId:
          data.builtUpArea && data.builtUpAreaUnitId
            ? data.builtUpAreaUnitId
            : null,
        builtUpAreaConversionFactor:
          data.builtUpArea && data.builtUpAreaUnitId
            ? Number(this.builtUpAreaConversion)
            : null,
        saleableArea: data.saleableArea || null,
        saleableAreaUnitId:
          data.saleableArea && data.saleableAreaUnitId
            ? data.saleableAreaUnitId
            : null,
        saleableAreaConversionFactor:
          data.saleableArea && data.saleableAreaUnitId
            ? Number(this.saleableAreaConversion)
            : null,
        propertyArea: data?.propertyArea || null,
        propertyAreaUnitId:
          data?.propertyArea && data?.propertyAreaUnitId
            ? data.propertyAreaUnitId
            : null,
        propertyAreaConversionFactor:
          data?.propertyArea && data?.propertyAreaUnitId
            ? Number(this.propertyAreaConversion)
            : null,

        netArea: data?.netArea || null,
        netAreaUnitId:
          data?.netArea && data?.netAreaUnitId
            ? data.netAreaUnitId
            : null,
        netAreaConversionFactor:
          data.netArea && data.netAreaUnitId
            ? Number(this.netAreaConversion)
            : null,
        unitName: data?.unitName,
        clusterName: data?.clusterName,
        propertyTypeIds: getPropertyTypeIds(
          this.propertyTypeList,
          data?.propertyTypeId,
          data?.propSubType
        ),
        possesionType: data?.possessionRange
          ? PossessionType[data?.possessionRange]
          : 0,
        prospectSourceId: data?.dataSource?.id || data?.dataSource,
      },
      propertiesList: data?.propertiesList,
      projectsList: data?.projectsList,
      referralContactNo: data.referralContactNo
        ? data.referralContactNo?.toString()
        : null,
      validateAllFormFields,
      referralName: data.referralName,
      referralEmail: data.referralEmail,
      dateOfBirth: data.dateOfBirth ? setTimeZoneDate(data.dateOfBirth, '00:00:00') : null,
      gender: data.gender ? data.gender : 0,
      maritalStatus: data.maritalStatus ? data.maritalStatus : 0,
    };

    if (
      this.globalSettingsDetails?.isCustomLeadFormEnabled &&
      !data.customerLocationId?.placeId
    ) {
      const location = data.customerLocationId?.location;

      if (location) {
        const [towerName, subCommunity, community, city] = location
          ?.split(',')
          ?.map((name: any) => name?.trim());
        payload.addressDto = {
          towerName: towerName ?? towerName,
          subCommunity: subCommunity ?? subCommunity,
          community: community ?? community,
          city: city ?? city,
        };
      }
    }

    if (this.selectedDataId) {
      payload = {
        ...this.selectedDataInfo,
        ...payload,
        statusId:
          this.selectedDataInfo?.status?.childType?.id ||
          this.selectedDataInfo?.status?.id,
      };
    }
    return payload;
  }

  goToData(shouldNavigateToAlternate: boolean = false): any {
    if (this.canEdit) {
      this.checkDuplicacy = false;
      this.checkAlternateNumDuplicacy = false;
      location.href =
        'data/edit-data/' +
        (shouldNavigateToAlternate
          ? this.duplicateDataAltId
          : this.duplicateDataId);
    }
  }

  navigateToManageData(): void {
    this.router.navigate(['data/manage-data']);
  }

  onScroll(event: Event): void {
    const container = event.target as HTMLElement;
    const containerTop = container.getBoundingClientRect().top;

    this.fieldSections.forEach((section: { name: string }, index: any) => {
      const sectionElement = document.getElementById(section.name);

      if (sectionElement && !this.scrollpause) {
        const sectionRect = sectionElement.getBoundingClientRect();
        const sectionTop = sectionRect.top - containerTop - 100;
        const sectionBottom = sectionRect.bottom - containerTop - 100;
        if (sectionTop <= 0 && sectionBottom > 0) {
          this.currentActive = index;
        }
      }
    });
  }
  goToManageData() {
    return this.router.navigate(['data/manage-data']);
  }
  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Backspace' || event.keyCode === 8) {
      this.isBackSpace = true;
    } else {
      this.isBackSpace = false;
    }
  }

  scrollTo(sectionName: string, index: number): void {
    this.currentActive = index;
    this.scrollpause = true;
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
      targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setTimeout(() => {
        this.scrollpause = false;
      }, 1000);
    }
  }

  onUnitChange(unit: any) {
    const carpetAreaUnitId = this.addDataForm.get(unit).value;
    this.addDataForm.controls[unit]?.setValue(null);
    if (
      !this.addDataForm.get('saleableAreaUnitId').value &&
      !this.addDataForm.get('builtUpAreaUnitId').value &&
      !this.addDataForm.get('carpetAreaUnitId').value &&
      !this.addDataForm.get('propertyAreaUnitId').value &&
      !this.addDataForm.get('netAreaUnitId').value

    ) {
      this.addDataForm.controls['saleableAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addDataForm.controls['builtUpAreaUnitId'].setValue(carpetAreaUnitId);
      this.addDataForm.controls['carpetAreaUnitId'].setValue(carpetAreaUnitId);
      this.addDataForm.controls['propertyAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addDataForm.controls['netAreaUnitId'].setValue(
        carpetAreaUnitId
      );
    } else {
      this.addDataForm.controls[unit].setValue(carpetAreaUnitId);
    }
  }

  confirmDuplicate() {
    if (
      !this.addDataForm.valid ||
      (this.checkDuplicacy && this.numberEdited) ||
      (this.checkAlternateNumDuplicacy && this.alternateNumberEdited) ||
      this.addDataForm.value.lowerBudget > this.addDataForm.value.upperBudget
    ) {
      validateAllFormFields(this.addDataForm);
      return;
    }
    const initialState: any = {
      type: 'leadDuplicate',
      data: {
        fieldType: 'Warning',
        heading: `Duplicate Data Detected!`,
        message:
          'Data with the same "Primary Number/Alternate Number" is already present in CRM.',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    const modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        { class: 'modal-400 top-modal ph-modal-unset', initialState }
      )
    );
    modalRef.onHide.subscribe((reason: string) => {
      if (reason === 'confirmed') {
        this.postData();
      }
    });
  }

  patchDefaultUnits() {
    const defaultUnits = {
      carpetAreaUnitId: this.addDataForm?.get('carpetAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      builtUpAreaUnitId: this.addDataForm?.get('builtUpAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      saleableAreaUnitId: this.addDataForm?.get('saleableAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      netAreaUnitId: this.addDataForm?.get('netAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      propertyAreaUnitId: this.addDataForm?.get('propertyAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
    };
    this.addDataForm?.patchValue(defaultUnits);
  }

  monthChanged(event: Date): void {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.addDataForm.controls['customPossessionDate'].setValue(
      this.selectedMonthAndYear
    );
    this.addDataForm.controls['possessionDate'].setValue(this.selectedMonthAndYear);
    this.addDataForm.controls['possessionRange'].setValue('Custom Date');

    // Mark the form as dirty to ensure changes are saved
    this.addDataForm.controls['customPossessionDate'].markAsDirty();
    this.addDataForm.controls['possessionDate'].markAsDirty();
    this.addDataForm.controls['possessionRange'].markAsDirty();

    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.isOpenPossessionModal = false;
    this.dt5.close();
  }

  calculateDateRange(value: string): void {
    const currentDate = new Date(this.currentDate);
    let startDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );
    let endDate: Date;

    if (value === '6 Months') {
      endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    } else if (value === '1 Year') {
      endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), 0);
    } else if (value === '2 Years') {
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    } else if (value === 'Under Construction') {
      // For Under Construction, use current date + 2 years as a default
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    }

    this.addDataForm.controls['customPossessionDate'].setValue(endDate);
  }

  convertDateToMonth(data: any) {
    if (!data) return;
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  closePossesion() {
    if (
      this.addDataForm.controls['possessionRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      return;
    }
    this.isOpenPossessionModal = false;
  }

  handlePossessionRangeChange(value: string): void {
    this.addDataForm.get('possessionRange')?.setValue(value);
    this.addDataForm.markAsDirty();

    if (value === 'Custom Date') {
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.selectedPossession = 'Custom Date';
      this.addDataForm.controls['customPossessionDate'].setValue(null);
      this.isOpenPossessionModal = true;
      this.isValidPossDate = false;
    } else if (
      value === '6 Months' ||
      value === '1 Year' ||
      value === '2 Years'
    ) {
      this.calculateDateRange(value);
      const endDate = this.addDataForm.get('customPossessionDate').value;
      this.addDataForm.controls['possessionDate'].setValue(endDate);
      this.addDataForm.controls['possessionDate'].markAsTouched();
      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isValidPossDate = false;
      this.isOpenPossessionModal = false;
    } else if (value === 'Under Construction') {
      this.addDataForm.controls['customPossessionDate'].setValue(null);
      this.addDataForm.controls['possessionDate'].setValue(null);
      this.selectedPossession = value;
      this.isOpenPossessionModal = false;
    }
  }

  // New Possession Needed By methods
  handlePossessionNeededByChange(value: string): void {
    this.addDataForm.get('possessionNeededBy')?.setValue(value);
    this.addDataForm.markAsDirty();

    if (value === 'Custom') {
      this.selectedPossessionNeededBy = 'Custom - Select Date';
      this.addDataForm.controls['customPossessionNeededByDate'].setValue(null);
      this.isValidPossessionNeededByDate = false;
      // Keep modal open for custom date selection
    } else {
      this.selectedPossessionNeededBy = value;
      this.addDataForm.controls['customPossessionNeededByDate'].setValue(null);
      this.isValidPossessionNeededByDate = false;
      // Auto-close modal for non-custom selections
      this.isOpenPossessionNeededByModal = false;
    }
  }

  closePossessionNeededBy(): void {
    if (
      this.addDataForm.controls['possessionNeededBy'].value === 'Custom' &&
      !this.addDataForm.controls['customPossessionNeededByDate'].value
    ) {
      this.isValidPossessionNeededByDate = true;
      return;
    }
    this.isOpenPossessionNeededByModal = false;
  }

  onCustomPossessionDateSelected(event: any): void {
    if (event) {
      const selectedDate = new Date(event);
      const formattedDate = selectedDate.toLocaleDateString('en-GB'); // dd/mm/yyyy format
      this.selectedPossessionNeededBy = `Custom - ${formattedDate}`;
      this.isValidPossessionNeededByDate = false;
      // Auto-close modal when custom date is selected
      this.isOpenPossessionNeededByModal = false;
    }
  }

  openPreview(): void {
    this.modalRef = this.modalService.show(this.previewModal, {
      class: 'modal-600 modal-dialog-centered ip-modal-unset',
    });
  }

  onSelectSource(source: any) {
    if (source) {
      this.addDataForm.get('dataSource').setValue(source, { emitEvent: false });
      this.updateSubSources(source.displayName);
      this.addDataForm.get('subSource').reset()
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSources = this.subSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        this.subSources = this.subSourceList[formattedKey] || [];
      }
    } else {
      let subSourceList: string[] = this.sourceList?.flatMap((lead: any) => {
        if (lead?.displayName === '99 Acres') {
          return this.subSourceList['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.subSourceList[formattedKey];
        if (!match) {
          match = this.subSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.subSourceList['NinetyNineAcres'];
        }
        return match ? match : [];
      }) || [];
      this.subSources = subSourceList
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
