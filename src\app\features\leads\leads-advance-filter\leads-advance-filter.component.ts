import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, firstValueFrom, skipWhile, takeUntil } from 'rxjs';
import {
  BHK_NO_ALL,
  BHK_TYPE,
  CALL_DIRECTIONS,
  CALL_STATUSES,
  DATE_TYPE,
  DONE_STATUS_LIST,
  FURNISH_STATUS,
  GENDER,
  LEAD_FILTER_TYPES,
  LEADS_VISIBILITY_V2,
  MARITAL_STATUS,
  NUMBER_10,
  OFFERING_TYPE,
  OWNER_TYPE,
  PAYMENT_MODES,
  PAYMENT_TYPE,
  POSSESSION_NEEDED_BY_LIST,
  PURPOSE_LIST,
  TRANSACTION_TYPE_LIST,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadDateType,
  LeadSource,
  OwnerSelectionType,
  PossessionType,
  Profession,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { MasterAreaUnitType } from 'src/app/core/interfaces/master-data.interface';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  generateFloorOptions,
  getBHKDisplayString,
  getBRDisplayString,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  onlyNumbers,
  onlyNumbersWithDecimal,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  validateAllFormFields
} from 'src/app/core/utils/common.util';
import { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAdditionalPropertyList,
  FetchAdditionalPropertyValue,
  FetchAgencyNameList,
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchLeadAltCountryCode,
  FetchLeadCities,
  FetchLeadClusterName,
  FetchLeadCommunities,
  FetchLeadCountries,
  FetchLeadCountryCode,
  FetchLeadCurrency,
  FetchLeadLandLine,
  FetchLeadLocalites,
  FetchLeadNationality,
  FetchLeadPostalCode,
  FetchLeadStates,
  FetchLeadSubCommunities,
  FetchLeadTowerNames,
  FetchLeadUnitName,
  FetchLeadZones,
  FetchLocations,
  FetchProjectList,
  FetchPropertyList,
  FetchSubSourceList,
  FetchUploadTypeNameList,
  UpdateFilterPayload,
  UpdateInvoiceFilter,
} from 'src/app/reducers/lead/lead.actions';
import {
  getAdditionalProperty,
  getAdditionalPropertyIsLoading,
  getAdditionalPropertyValue,
  getAdditionalPropertyValueIsLoading,
  getAgencyNameList,
  getAgencyNameListIsLoading,
  getCampaignList,
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getFiltersPayload,
  getInvoiceFiltersPayload,
  getIsLeadCustomStatusEnabled,
  getLeadAltCountryCode,
  getLeadAltCountryCodeLoading,
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadClusterName,
  getLeadClusterNameLoading,
  getLeadCommunities,
  getLeadCommunitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadCountryCode,
  getLeadCountryCodeLoading,
  getLeadCurrencyList,
  getLeadCurrencyListIsLoading,
  getLeadLandLine,
  getLeadLandLineLoading,
  getLeadLocalites,
  getLeadLocalitesIsLoading,
  getLeadNationality,
  getLeadNationalityLoading,
  getLeadPostalCode,
  getLeadStates,
  getLeadStatesIsLoading,
  getLeadSubCommunities,
  getLeadSubCommunitiesIsLoading,
  getLeadTowerNames,
  getLeadTowerNamesIsLoading,
  getLeadUnitName,
  getLeadUnitNameLoading,
  getLeadZones,
  getLeadZonesIsLoading,
  getLocations,
  getLocationsIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getPropertyList,
  getPropertyListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
  getUploadTypeNameList,
  getUploadTypeNameListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits,
} from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import {
  FetchReportees,
  FetchUsersByDesignation,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getReportees,
  getReporteesIsLoading,
  getUserBasicDetails,
  getUsersByDesignation,
  getUsersByDesignationIsLoading,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'leads-advance-filter',
  templateUrl: './leads-advance-filter.component.html',
})
export class LeadsAdvanceFilterComponent implements OnInit {
  @Output() filterChanged: EventEmitter<any> = new EventEmitter();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('possessionFilter') possessionFilter: any;
  @ViewChild('dt2') dt2: OwlDateTimeComponent<any>;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  filtersForm: FormGroup;
  leadVisibility: Array<any> = Object.values(LEADS_VISIBILITY_V2).slice(0, 3);
  dateTypeList: Array<string> = DATE_TYPE;
  onPickerOpened = onPickerOpened;
  getBHKDisplayString = getBHKDisplayString;
  formatBudget = formatBudget;
  getBRDisplayString = getBRDisplayString;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  profession: Array<Profession | string> = Object.values(Profession).slice(
    1,
    10
  );
  currentDate: Date = new Date();
  currentPath: any;
  doneStatusList = DONE_STATUS_LIST;
  reportees: any[] = [];
  allUsersList: any[] = [];
  leadSources: Array<string> = [];
  enquiryType: Array<any> = TRANSACTION_TYPE_LIST;
  propertyType: Array<any> = JSON.parse(localStorage.getItem('propertyType'));
  subSourceList: any = [];
  allSubSourceList: any = [];
  agencyNameList: any;
  channelPartnerList: Array<any>;
  propertySubTypes: Array<Object> = [];
  bhkType: Array<string> = BHK_TYPE;
  noOfBhk: Array<string> = BHK_NO_ALL;
  projectList: Array<string> = [];
  propertyList: Array<string> = [];
  numbers: Array<{ display: string; value: number }> = NUMBER_10;
  numbers10: Array<{ display: string; value: number }> = NUMBER_10.slice(1);
  furnishStatus = FURNISH_STATUS;
  floorOptions: string[] = generateFloorOptions();
  offerType: Array<{ displayName: string; value: number }> = OFFERING_TYPE;
  purposeList: Array<{ displayName: string; value: number }> = PURPOSE_LIST;
  locations: string[];
  cities: string[];
  states: string[];
  countries: string[];
  postalCodeList: string[];
  landLineList: string[];
  isPostalCodeLoading: boolean = false;
  isLandLineLoading: boolean = false;
  subCommunities: string[];
  communities: string[];
  towerNames: string[];
  localites: string[];
  zones: string[];
  uploadTypeList: any[] = [];
  isUntouched = [
    { name: 'Yes', value: 'Yes' },
    { name: 'No', value: 'No' },
  ];
  showParentChild = [
    { name: 'Yes', value: true },
    { name: 'No', value: false },
  ];
  designationList: { id: any; name: string }[];
  allUsers: any[] = [];
  leadCurrency: any[] = [];
  areaSizeUnits: Array<MasterAreaUnitType> = [];
  additionalProperty: any;
  additionalPropertyValue: any;
  globalSettingsData: any;
  leadTags: Array<any> = [];
  userData: any;
  defaultCurrency: any;
  currency: any;
  permission: Set<unknown>;
  isAreaUnitsLoading: any;
  dateType: string;
  carpetAreaValidations: boolean = true;
  buildUpAreaValidations: boolean = true;
  saleableValidation: boolean = true;
  netValidation: boolean = true;
  areaValidation: boolean = true;
  filterData: any;
  canViewAllUsers: boolean = false;
  ownerSelection: Array<any> = OWNER_TYPE

  // New Possession Needed By properties
  isOpenPossessionNeededByModal: boolean = false;
  selectedPossessionNeededBy: string = '';
  possessionNeededByList = POSSESSION_NEEDED_BY_LIST;
  isValidPossessionNeededByDate: boolean = false;

  formFields: any = {
    dateType: [0],
    fromDate: [null],
    toDate: [null],
    leadVisibility: [0],
    assignTo: [null],
    AssignedFromIds: [null],
    SecondaryFromIds: [null],
    DoneBy: [null],
    SecondaryUsers: [null],
    ownerSelection: [null],
    OriginalOwner: [null],
    Source: [null],
    SubSources: [null],
    Baths: [null],
    NoOfBHKs: [null],
    BHKTypes: [null],
    PropertySubType: [null],
    PropertyType: [null],
    ChannelPartnerNames: [null],
    CampaignNames: [null],
    AgencyNames: [null],
    enquiredFor: [null],
    Beds: [null],
    Furnished: [null],
    Floors: [null],
    OfferTypes: [null],
    Purposes: [null],
    Locations: [null],
    Cities: [null],
    States: [null],
    Countries: [null],
    PostalCodes: [null],
    SubCommunities: [null],
    Communities: [null],
    TowerNames: [null],
    Localities: [null],
    createdByIds: [null],
    bookedByIds: [null],
    lastModifiedByIds: [null],
    archivedByIds: [null],
    restoredByIds: [null],
    Properties: [null],
    Projects: [null],
    designationsId: [null],
    IsUntouched: [null],
    UploadTypeName: [null],
    Latitude: [null],
    Longitude: [null],
    RadiusInKm: [null],
    Zones: [null],
    Nationality: [null],
    ClusterName: [null],
    closingManagers: [null],
    sourcingManagers: [null],
    Profession: [null],
    FromMinBudget: [null],
    ToMinBudget: [null],
    FromMaxBudget: [null],
    ToMaxBudget: [null],
    Currency: [null],
    AdditionalPropertiesKey: [null],
    AdditionalPropertiesValue: [null],
    ReferralName: [null],
    ReferralContactNo: [null],
    ReferralEmail: [null],
    LandLine: [null],
    MinCarpetArea: [null],
    MaxCarpetArea: [null],
    CarpetAreaUnitId: [null],
    BuiltUpAreaUnitId: [null],
    MaxBuiltUpArea: [null],
    MinBuiltUpArea: [null],
    SaleableAreaUnitId: [null],
    MaxSaleableArea: [null],
    MinSaleableArea: [null],
    MinPropertyArea: [null],
    MaxPropertyArea: [null],
    PropertyAreaUnitId: [null],
    MinNetArea: [null],
    MaxNetArea: [null],
    NetAreaUnitId: [null],
    UnitNames: [null],
    CountryCode: [null],
    AltCountryCode: [null],
    IsWithTeam: [false],
    IsWithHistory: [false],
    StatusIds: [null],
    SubStatusIds: [null],
    MeetingOrVisitStatuses: [null],
    FromDateForMeetingOrVisit: [null],
    ToDateForMeetingOrVisit: [null],
    AppointmentDoneByUserIds: [null],
    CustomFlags: [null],
    DataConverted: [null],
    QualifiedByIds: [null],
    ConfidentialNotes: [null],
    ChildLeadsCount: [null],
    LeadType: [[]],
    CallDirections: [null],
    CallStatuses: [null],
    UserIds: [null],
    possessionNeededBy: [null],
    customPossessionNeededByDate: [null],
    ShowPrimeLeads: [null],
    genderTypes: [null],
    maritalStatuses: [null],
    dateOfBirth: [null],
  };
  filtersPayload: any;
  filterDate: any = [];
  customStatusList: any = [];
  masterLeadStatus: any = [];
  subStatusList: any = [];
  isCustomStatusEnabled: any;
  DateForMeetingOrVisit: any = [];
  PaymentModeList: any = PAYMENT_MODES;
  PaymentTypeList: any = PAYMENT_TYPE;
  campaigns: Array<any>;
  isLocationListLoading: boolean;
  isLeadCitiesLoading: boolean;
  isLeadStatesLoading: boolean;
  isLeadCountriesLoading: boolean;
  isLeadLocalitiesLoading: boolean;
  isLeadZonesLoading: boolean;
  isLeadTowerNamesLoading: boolean;
  isAgencyNameListLoading: boolean;
  isProjectListLoading: boolean;
  isChannelPartnerLoading: boolean;
  isUploadTypeNameListIsLoading: boolean;
  isLeadCommunitiesIsLoading: boolean;
  isLeadSubCommunitiesIsLoading: boolean;
  isUsersListForReassignmentIsLoading: boolean;
  isReporteesIsLoading: boolean;
  isLeadCurrencyListIsLoading: boolean;
  isAdditionalPropertyIsLoading: boolean;
  isAdditionalPropertyValueIsLoading: boolean;
  isUsersByDesignationIsLoading: boolean;
  isSubSourceListIsLoading: boolean;
  isPropertyListIsLoading: boolean;
  isCustomStatusListIsLoading: boolean;
  isLeadNationalityLoading: boolean;
  nationalities: any[] = [];
  isLeadUnitNameLoading: boolean;
  isLeadClusterLoading: boolean;
  clusterNames: any[] = [];
  unitNames: any[] = [];
  isSourcesLoading: boolean;
  minBudgetValidation: boolean = true;
  maxBudgetValidation: boolean = true;
  countryCodes: any[] = [];
  isLeadCountryCodeLoading: boolean;
  isLeadAltCountryCodeLoading: boolean;
  altCountryCodes: any[] = [];
  leadFilterTypes: any = LEAD_FILTER_TYPES;
  gender: any[] = GENDER;
  maritalStatus: any[] = MARITAL_STATUS;
  callDateFilter: any = ['Today', 'Yesterday', 'Last 7 Days', 'Custom Date'];
  selectedCallDateType: any = null;
  callDate: any = [null, null];


  get isShowParentLead(): boolean {
    return this.getFormValue('LeadType')?.includes(1);
  }
  isClosePossessionModal: boolean = false;
  selectedPossession: string | null = null;
  customDateValidation: boolean = true;
  callDirections = CALL_DIRECTIONS;
  callStatuses = CALL_STATUSES;

  get maxDate(): Date {
    const maxDate = new Date(this.currentDate);
    maxDate.setHours(0, 0, 0, 0);
    return maxDate;
  }

  get showOwnerType(): boolean {
    return this.getFormValue('assignTo')?.length;

  }

  constructor(
    public router: Router,
    private _store: Store<AppState>,
    public modalService: BsModalService,
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    public trackingService: TrackingService
  ) { }

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this.PaymentModeList = this.PaymentModeList.map(
      (mode: any, index: number) => ({ label: mode, value: index + 1 })
    );
    this.PaymentTypeList = this.PaymentTypeList.map(
      (mode: any, index: number) => ({ label: mode, value: index + 1 })
    );
    this.initializeForms();
    this.filtersForm.get('Currency')?.valueChanges.subscribe((newCurrency) => {
      this.filtersForm
        .get('Currency')
        ?.setValue(newCurrency, { emitEvent: false });
    });
    this.initializeDispatch();
    this.setupSubscriptions();
    this.initializeFilters();
  }

  private initializeFilters(): void {
    setTimeout(() => {
      if (this.filtersPayload?.PossesionType) {
        const possessionValue = this.filtersPayload.PossesionType;
        const possessionType = PossessionType[possessionValue] as string;
        if (possessionType) {
          this.selectedPossession = possessionType;
          this.filtersForm.patchValue({
            PossesionType: possessionValue
          });

          if (possessionType === 'Custom Date') {
            // Initialize FromPossesionDate and ToPossesionDate
            if (this.filtersPayload.FromPossesionDate) {
              this.filtersForm.patchValue({
                FromPossesionDate: this.filtersPayload.FromPossesionDate
              });
            }

            if (this.filtersPayload.ToPossesionDate) {
              this.filtersForm.patchValue({
                ToPossesionDate: this.filtersPayload.ToPossesionDate
              });
            }
          }
        }
      }
    }, 0);
  }

  getFormValue(controlName: string) {
    return this.filtersForm.get(controlName)?.value;
  }

  initializeForms() {
    this.formFields = {
      ...this.formFields,
      ...(this.currentPath === '/invoice'
        ? {
          Gst: [null, [Validators.min(1), Validators.max(99)]],
          TotalBrokerage: [null],
          EarnedBrokerage: [null],
          NetBrokerageAmount: [null],
          BrokerageCharges: [null],
          PaymentType: [null],
          RemainingAmount: [null],

          LowerDiscountLimit: [null],
          PaymentMode: [null],
          SoldPrice: [null],
          AdditionalCharges: [null],
          CarParkingCharges: [null],
          LowerAgreementLimit: [null],
          BookedUnderName: [null],
        }
        : {}),
    };
    this.filtersForm = this.fb.group(this.formFields);
  }

  minBudgetCheck(): void {
    const minBudget = this.getFormValue('FromMinBudget');
    const maxBudget = this.getFormValue('ToMinBudget');

    if (minBudget !== null && maxBudget !== null && maxBudget < minBudget) {
      this.minBudgetValidation = false;
    } else {
      this.minBudgetValidation = true;
    }
  }

  maxBudgetCheck(): void {
    const minBudget = this.getFormValue('FromMaxBudget');
    const maxBudget = this.getFormValue('ToMaxBudget');

    if (minBudget && maxBudget && maxBudget < minBudget) {
      this.maxBudgetValidation = false;
    } else {
      this.maxBudgetValidation = true;
    }
  }

  initializeDispatch() {
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchPropertyList(true));
    this._store.dispatch(new FetchProjectList(true));
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store.dispatch(new FetchReportees());
    this._store.dispatch(new FetchLocations());
    this._store.dispatch(new FetchLeadLocalites());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchLeadZones());
    this._store.dispatch(new FetchAgencyNameList());
    this._store.dispatch(new FetchLeadCurrency());
    this._store.dispatch(new FetchUploadTypeNameList());
    this._store.dispatch(new FetchAdditionalPropertyList());
    this._store.dispatch(new FetchChannelPartnerList());
    this._store.dispatch(new FetchUsersByDesignation());
    this._store.dispatch(new FetchCampaignList());
    this._store.dispatch(new FetchLeadPostalCode());
    this._store.dispatch(new FetchLeadLandLine());
    this._store.dispatch(new FetchAllSources());
    this._store.dispatch(new FetchLeadCountryCode());
    this._store.dispatch(new FetchLeadAltCountryCode());
    // this._store.dispatch(new FetchLeadPostalCode());
  }

  async setupSubscriptions() {
    this.propertySubTypes = this.propertyType
      ?.map((type: any) => type?.childTypes)
      .flat();
    this.isCustomStatusEnabled = await firstValueFrom(
      this._store.select(getIsLeadCustomStatusEnabled)
    );
    if (!this.isCustomStatusEnabled) {
      this.masterLeadStatus = JSON.parse(
        localStorage.getItem('masterleadstatus') || '[]'
      );
      this.subStatusList = this.masterLeadStatus
        ?.map((status: any) => status?.childTypes)
        .flat();
    } else {
      this._store
        .select(getCustomStatusList)
        .pipe(takeUntil(this.stopper))
        .subscribe((customStatus: any) => {
          this.customStatusList = customStatus
            ?.slice()
            .sort((a: any, b: any) =>
              a?.displayName.localeCompare(b?.displayName)
            );
          this.subStatusList = this.customStatusList
            ?.map((status: any) => status?.childTypes)
            ?.flat();
        });
    }
    this._store
      .select(getTagsList)
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !data?.length)
      )
      .subscribe((data: any) => {
        this.leadTags = data
          ?.filter((flag: any) => flag?.isActive)
          .sort((a: any, b: any) =>
            a?.name.toLowerCase().localeCompare(b?.name.toLowerCase())
          );
      });

    const permission = await firstValueFrom(
      this._store.select(getPermissions).pipe(
        takeUntil(this.stopper),
        skipWhile((data) => !data?.length)
      )
    );

    this.permission = new Set(permission);
    this.canViewAllUsers =
      this.permission.has('Permissions.Users.ViewForFilter') &&
      this.permission.has('Permissions.Leads.ViewAllLeads');

    const visibilityChecks = [
      { permission: 'Permissions.Leads.ViewUnAssignedLead', index: 3 },
      { permission: 'Permissions.Leads.Delete', index: 4 },
      { permission: 'Permissions.Leads.ViewDuplicateTag', index: 5 },
    ];

    visibilityChecks.forEach(({ permission, index }) => {
      if (
        this.permission.has(permission) &&
        !this.leadVisibility?.some(
          (item: any) => item?.name === LEADS_VISIBILITY_V2[index].displayName
        ) &&
        this.currentPath !== '/invoice'
      ) {
        this.leadVisibility = [
          ...this.leadVisibility,
          LEADS_VISIBILITY_V2[index],
        ];
      }
    });

    const subscriptions = [
      {
        selector:
          this.currentPath === '/invoice'
            ? getInvoiceFiltersPayload
            : getFiltersPayload,
        handler: (filters: any) => {
          this.filtersPayload = filters;
          this.filtersForm.patchValue({
            ...filters,
            Profession: filters.Profession?.map(
              (item: any) => Profession[item]
            ),
            ownerSelection: OwnerSelectionType[filters?.ownerSelection],
            Currency: filters.Currency ?? this.defaultCurrency,
            assignTo: filters?.assignTo ?? filters?.HistoryAssignedToIds,
            ...(this.filterData && Object.keys(this.filterData).length ? this.filterData?.filterCriteria : {})
          });
          this.dateType = LeadDateType[filters.dateType] ?? 'All';
          this.filterDate = [
            patchTimeZoneDate(
              filters?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              filters?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ];
          this.callDate = [
            patchTimeZoneDate(
              filters?.CallLogFromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              filters?.CallLogToDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
          ]
          this.selectedCallDateType = filters?.CallDateType;
          this.DateForMeetingOrVisit = [
            patchTimeZoneDate(
              filters?.FromDateForMeetingOrVisit,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              filters?.ToDateForMeetingOrVisit,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ];
          this.onStatusChange();
          this.updateSubSource();
        },

      },
      {
        selector: getUserBasicDetails,
        handler: (data: any) => {
          this.userData = data;
          this.currentDate = changeCalendar(
            this.userData?.timeZoneInfo?.baseUTcOffset
          );
        },
      },
      {
        selector: getGlobalSettingsAnonymous,
        handler: (data: any) => {
          this.defaultCurrency = data.countries?.[0]?.defaultCurrency || null;
          this.currency =
            data.countries?.[0]?.currencies?.map((cur: any) => cur.symbol) ||
            null;
          this.globalSettingsData = data;
          this.filtersForm.patchValue({
            Currency: this.filtersPayload?.Currency ?? this.defaultCurrency,
          });
          if (this.globalSettingsData?.isCustomLeadFormEnabled) {
            this._store.dispatch(new FetchLeadSubCommunities());
            this._store.dispatch(new FetchLeadCommunities());
            this._store.dispatch(new FetchLeadTowerNames());
            this._store.dispatch(new FetchLeadNationality());
            this._store.dispatch(new FetchLeadClusterName());
            this._store.dispatch(new FetchLeadUnitName());
          }
        },
      },
      {
        selector: getAreaUnits,
        handler: this.handleAreaUnits.bind(this),
      },
      {
        selector: getAreaUnitIsLoading,
        handler: (data: boolean) => (this.isAreaUnitsLoading = data),
      },
      { selector: getLocations, handler: this.handleLocation.bind(this) },
      {
        selector: getLocationsIsLoading,
        handler: (data: boolean) => (this.isLocationListLoading = data),
      },
      { selector: getLeadCities, handler: this.handleCities.bind(this) },
      {
        selector: getLeadCitiesIsLoading,
        handler: (data: boolean) => (this.isLeadCitiesLoading = data),
      },
      { selector: getLeadStates, handler: this.handleState.bind(this) },
      {
        selector: getLeadStatesIsLoading,
        handler: (data: boolean) => (this.isLeadStatesLoading = data),
      },
      { selector: getLeadCountries, handler: this.handleCountries.bind(this) },
      { selector: getLeadPostalCode, handler: this.handlePostalCode.bind(this) },
      { selector: getLeadLandLine, handler: this.handleLandLine.bind(this) },
      {
        selector: getLeadPostalCode,
        handler: this.handlePostalCode.bind(this),
      },
      {
        selector: getLeadCountriesIsLoading,
        handler: (data: boolean) => (this.isLeadCountriesLoading = data),
      },
      {
        selector: getLeadLandLineLoading,
        handler: (data: boolean) => (this.isLandLineLoading = data),
      },
      { selector: getLeadTowerNames, handler: this.handleTowerName.bind(this) },
      {
        selector: getLeadTowerNamesIsLoading,
        handler: (data: boolean) => (this.isLeadTowerNamesLoading = data),
      },
      { selector: getLeadLocalites, handler: this.handleLocality.bind(this) },
      {
        selector: getLeadLocalitesIsLoading,
        handler: (data: boolean) => (this.isLeadLocalitiesLoading = data),
      },
      { selector: getLeadZones, handler: this.handleZones.bind(this) },
      {
        selector: getLeadZonesIsLoading,
        handler: (data: boolean) => (this.isLeadZonesLoading = data),
      },
      { selector: getAgencyNameList, handler: this.handleAgencies.bind(this) },
      {
        selector: getAgencyNameListIsLoading,
        handler: (data: boolean) => (this.isAgencyNameListLoading = data),
      },
      { selector: getProjectList, handler: this.handleProject.bind(this) },
      {
        selector: getProjectListIsLoading,
        handler: (data: boolean) => (this.isProjectListLoading = data),
      },
      {
        selector: getChannelPartnerList,
        handler: this.handleChannel.bind(this),
      },
      { selector: getCampaignList, handler: this.handleCampaign.bind(this) },
      {
        selector: getChannelPartnerListIsLoading,
        handler: (data: boolean) => (this.isChannelPartnerLoading = data),
      },
      {
        selector: getUploadTypeNameList,
        handler: this.handleUploadType.bind(this),
      },
      {
        selector: getUploadTypeNameListIsLoading,
        handler: (data: boolean) => (this.isUploadTypeNameListIsLoading = data),
      },
      {
        selector: getLeadCommunities,
        handler: this.handleCommunity.bind(this),
      },
      {
        selector: getLeadCommunitiesIsLoading,
        handler: (data: boolean) => (this.isLeadCommunitiesIsLoading = data),
      },
      {
        selector: getLeadSubCommunities,
        handler: this.handleSubCommunity.bind(this),
      },
      {
        selector: getLeadSubCommunitiesIsLoading,
        handler: (data: boolean) => (this.isLeadSubCommunitiesIsLoading = data),
      },
      {
        selector: getUsersListForReassignment,
        handler: this.handleUsersList.bind(this),
      },
      {
        selector: getUsersListForReassignmentIsLoading,
        handler: (data: boolean) =>
          (this.isUsersListForReassignmentIsLoading = data),
      },
      { selector: getReportees, handler: this.handleReportees.bind(this) },
      {
        selector: getReporteesIsLoading,
        handler: (data: boolean) => (this.isReporteesIsLoading = data),
      },
      {
        selector: getLeadNationality,
        handler: this.handleNationality.bind(this),
      },
      {
        selector: getLeadNationalityLoading,
        handler: (data: boolean) => (this.isLeadNationalityLoading = data),
      },
      {
        selector: getLeadClusterName,
        handler: this.handleClusterName.bind(this),
      },
      {
        selector: getLeadClusterNameLoading,
        handler: (data: boolean) => (this.isLeadClusterLoading = data),
      },
      { selector: getLeadUnitName, handler: this.handleUnitName.bind(this) },
      {
        selector: getLeadUnitNameLoading,
        handler: (data: boolean) => (this.isLeadUnitNameLoading = data),
      },
      { selector: getLeadCountryCode, handler: this.handleCountryCode.bind(this) },
      {
        selector: getLeadCountryCodeLoading,
        handler: (data: boolean) => (this.isLeadCountryCodeLoading = data),
      },
      { selector: getLeadAltCountryCode, handler: this.handleAltCountryCode.bind(this) },
      {
        selector: getLeadAltCountryCodeLoading,
        handler: (data: boolean) => (this.isLeadAltCountryCodeLoading = data),
      },
      {
        selector: getLeadCurrencyList,
        handler: (data: any) => {
          this.leadCurrency = data;
        },
      },
      {
        selector: getLeadCurrencyListIsLoading,
        handler: (data: boolean) => (this.isLeadCurrencyListIsLoading = data),
      },
      {
        selector: getAdditionalProperty,
        handler: (data: any) => {
          this.additionalProperty = data;
        },
      },
      {
        selector: getAdditionalPropertyIsLoading,
        handler: (data: boolean) => (this.isAdditionalPropertyIsLoading = data),
      },
      {
        selector: getAdditionalPropertyValue,
        handler: (data: any) => {
          this.additionalPropertyValue = data;
        },
      },
      {
        selector: getAdditionalPropertyValueIsLoading,
        handler: (data: boolean) =>
          (this.isAdditionalPropertyValueIsLoading = data),
      },
      {
        selector: getPropertyList,
        handler: this.handlePropertyList.bind(this),
      },
      {
        selector: getPropertyListIsLoading,
        handler: (data: boolean) => (this.isPropertyListIsLoading = data),
      },
      {
        selector: getUsersByDesignation,
        handler: (data: any) => {
          this.designationList = Object.entries(data)
            .map(([key, value]: [string, any]) => ({
              id: value[0]?.designation?.id,
              name: key,
            }))
            .filter((item) => item.name !== 'No Designation');
        },
      },
      {
        selector: getUsersByDesignationIsLoading,
        handler: (data: boolean) => (this.isUsersByDesignationIsLoading = data),
      },
      {
        selector: getSubSourceList,
        handler: (data: any) => {
          this.allSubSourceList = data;
          this.subSourceList = Object.values(data)
            .flat()
            .filter((data: any) => data)
            .slice()
            .sort((a: any, b: any) => a?.localeCompare(b));
          this.updateSubSource()
        },
      },
      {
        selector: getSubSourceListIsLoading,
        handler: (data: boolean) => (this.isSubSourceListIsLoading = data),
      },
      {
        selector: getCustomStatusListIsLoading,
        handler: (data: boolean) => (this.isCustomStatusListIsLoading = data),
      },
    ];
    subscriptions.forEach(({ selector, handler }) => {
      this._store
        .select(selector)
        .pipe(takeUntil(this.stopper))
        .subscribe(handler);
    });
    this.dateTypeList = this.dateTypeList?.filter((item: any) => item !== 'Possession Date');
    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });
    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
  }

  handleProject(data: any): void {
    this.projectList =
      data
        ?.filter((item: any) => item)
        ?.slice()
        ?.sort((a: any, b: any) => a.name?.localeCompare(b.name)) || [];
  }

  handleChannel(data: any): void {
    this.channelPartnerList = this.sortAndFilterList(data);
  }

  handleCampaign(data: any): void {
    this.campaigns = this.sortAndFilterList(data);
  }

  handleUploadType(data: any): void {
    this.uploadTypeList = this.sortAndFilterList(data);
  }

  handleAgencies(data: any): void {
    this.agencyNameList = this.sortAndFilterList(data);
  }

  handleZones(data: any): void {
    this.zones = this.sortAndFilterList(data);
  }

  handleLocality(data: any): void {
    this.localites = this.sortAndFilterList(data);
  }

  handleTowerName(data: any): void {
    this.towerNames = this.sortAndFilterList(data);
  }

  handleCountries(data: any): void {
    this.countries = this.sortAndFilterList(data);
  }

  handlePostalCode(data: any): void {
    this.postalCodeList = this.sortAndFilterList(data);
  }

  handleLandLine(data: any): void {
    this.landLineList = this.sortAndFilterList(data);
  }

  handleState(data: any): void {
    this.states = this.sortAndFilterList(data);
  }

  handleCities(data: any): void {
    this.cities = this.sortAndFilterList(data);
  }

  handleReportees(data: any): void {
    this.reportees = data;
    this.reportees = this.reportees?.map((user: any) => {
      user = {
        ...user,
        fullName: user.firstName + ' ' + user.lastName,
      };
      return user;
    });
    this.reportees = assignToSort(this.reportees, '');
  }

  handleUsersList(data: any): void {
    const sortedUsers = data?.map((user: any) => ({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`,
    }));
    this.allUsers = sortedUsers.sort(
      (a: any, b: any) =>
        (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
    );
    this.allUsersList = data?.map((user: any) => ({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`,
    }));
    this.allUsersList = assignToSort(this.allUsersList, '');
  }

  handlePropertyList(data: any): void {
    this.propertyList =
      data
        ?.filter((item: any) => item)
        ?.slice()
        ?.sort((a: any, b: any) => a.title?.localeCompare(b.title)) || [];
  }

  handleLocation(data: any): void {
    this.locations = this.sortAndFilterList(data);
  }

  handleCommunity(data: any): void {
    this.communities = this.sortAndFilterList(data);
  }

  handleSubCommunity(data: any): void {
    this.subCommunities = this.sortAndFilterList(data);
  }

  handleNationality(data: any): void {
    this.nationalities = this.sortAndFilterList(data);
  }

  handleClusterName(data: any): void {
    this.clusterNames = this.sortAndFilterList(data);
  }

  handleUnitName(data: any): void {
    this.unitNames = this.sortAndFilterList(data);
  }

  handleCountryCode(data: any): void {
    this.countryCodes = this.sortAndFilterList(data);
  }

  handleAltCountryCode(data: any): void {
    this.altCountryCodes = this.sortAndFilterList(data);

  }

  handleAreaUnits(areaUnits: any): void {
    this.areaSizeUnits = areaUnits;
  }

  sortAndFilterList(list: any[]): string[] {
    return (
      list
        ?.filter((item) => item)
        ?.slice()
        ?.sort((a, b) => a.localeCompare(b)) || []
    );
  }

  toggleHistoryTeam(input: string) {
    const resetField = input === 'history' ? 'IsWithTeam' : 'IsWithHistory';
    this.filtersForm.patchValue({
      [resetField]: false,
    });
  }

  fbPropertyChange(selectedKey: string) {
    this._store.dispatch(new FetchAdditionalPropertyValue(selectedKey));
  }

  dateChange(): void {
    if (this.dateType && this.filterDate?.[0]) {
      const fromDate = setTimeZoneDate(
        this.filterDate?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      const toDate = setTimeZoneDate(
        this.filterDate?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      this.filtersForm.patchValue({
        dateType: LeadDateType[this.dateType as keyof typeof LeadDateType],
        fromDate: fromDate,
        toDate: toDate,
      });
    }
    if (this.dateType) {
      this.trackingService.trackFeature(
        `Web.Leads.Filter.${this.dateType.replace(/\s+/g, '')}.Click`
      );
    } else if (this.filterDate?.[0]) {
      this.trackingService.trackFeature(`Web.Leads.Filter.CalenderRange.Click`);
    }
  }

  onClearAllFilters() {
    this.filtersForm.reset();
    this.filtersForm.patchValue({
      leadVisibility: 0,
      dateType: 0,
      Currency: this.defaultCurrency,
      FromPossesionDate: null,
      ToPossesionDate: null,
      PossesionType: null
    });
    this.dateType = 'All';
    this.filterDate = [];
    this.selectedPossession = null;
    this.customDateValidation = true;
    if (this.possessionFilter) {
      this.possessionFilter.reset();
    }
    this.trackingService.trackFeature(`Web.Leads.Filter.Reset.Click`);
  }

  applyAdvancedFilter() {
    if (
      ((this.getFormValue('Gst') < 1 || this.getFormValue('Gst') > 99) &&
        this.getFormValue('Gst') !== null) ||
      !this.filtersForm.valid
    ) {
      validateAllFormFields(this.filtersForm);
      return;
    }
    if (
      !this.minBudgetValidation ||
      !this.maxBudgetValidation ||
      !this.carpetAreaValidations ||
      !this.buildUpAreaValidations ||
      !this.saleableValidation ||
      !this.netValidation ||
      !this.areaValidation
    ) {
      return;
    }
    const formValues = this.filtersForm.value;

    this.filtersPayload = {
      ...this.filtersPayload,
      ...formValues,
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      pageNumber: 1,
      ...(formValues.IsWithHistory
        ? { HistoryAssignedToIds: formValues.assignTo, assignTo: null }
        : {}),
      ...(formValues.LowerDiscountLimit
        ? {
          UpperDiscountLimit: formValues.LowerDiscountLimit,
        }
        : {}),
      ...(formValues.LowerAgreementLimit
        ? {
          UpperAgreementLimit: formValues.LowerAgreementLimit,
        }
        : {}),
      ...(!formValues.MeetingOrVisitStatuses
        ? {
          AppointmentDoneByUserIds: null,
          ToDateForMeetingOrVisit: null,
          FromDateForMeetingOrVisit: null,
        }
        : {}),
      ...(formValues.Profession
        ? {
          Profession: formValues.Profession?.map(
            (profession: any) => Profession[profession]
          ),
        }
        : {}),
      ...(formValues.LeadType?.includes(1) ? { ChildLeadsCount: formValues.ChildLeadsCount } : { ChildLeadsCount: null }),
      ...(formValues.ownerSelection && formValues?.assignTo?.length
        ? {
          ownerSelection: OwnerSelectionType[formValues.ownerSelection]
        } : null),
      dateOfBirth: formValues.dateOfBirth ? setTimeZoneDate(formValues.dateOfBirth, '00:00:00') : null,
      CallLogFromDate: this.callDate[0] ? setTimeZoneDate(this.callDate[0], this.userData?.timeZoneInfo?.baseUTcOffset) : null,
      CallLogToDate: this.callDate[1] ? setTimeZoneDate(this.callDate[1], this.userData?.timeZoneInfo?.baseUTcOffset) : null,
      CallDateType: this.selectedCallDateType,
    }
    if (
      !this.getFormValue('FromMinBudget') &&
      !this.getFormValue('ToMinBudget') &&
      !this.getFormValue('FromMaxBudget') &&
      !this.getFormValue('ToMaxBudget')
    ) {
      this.filtersPayload.Currency = null;
    }

    this._store.dispatch(
      this.currentPath === '/invoice'
        ? new UpdateInvoiceFilter(this.filtersPayload)
        : new UpdateFilterPayload(this.filtersPayload)
    );
    this.filterChanged.emit(true);
    this.modalRef.hide();
    this.trackingService.trackFeature(`Web.Leads.Filter.Search.Click`);
  }
  onStatusChange() {
    const statusList = this.isCustomStatusEnabled
      ? this.customStatusList
      : this.masterLeadStatus;
    this.subStatusList = this.getFormValue('StatusIds')?.length
      ? statusList
        .filter((status: any) =>
          this.getFormValue('StatusIds').includes(status.id)
        )
        .flatMap((status: any) => status.childTypes || [])
      : statusList.flatMap((status: any) => status.childTypes || []);

    const substatusIds = new Set(
      this.subStatusList.map((subStatus: any) => subStatus?.id)
    );
    const selectedSubStatusIds = (
      this.getFormValue('SubStatusIds') || []
    ).filter((id: string) => substatusIds.has(id));

    this.filtersForm.patchValue({ SubStatusIds: selectedSubStatusIds });
  }

  updateSubSource() {
    this.subSourceList = [];
    const sourceMap: { [key: string]: string } = {
      GoogleSheets: 'GoogleSheet',
      '99Acres': 'NinetyNineAcres',
    };
    const cleanKey = (key: string): string => key?.replace(/\s+/g, '');
    const getMappedKey = (key: any): string => sourceMap[key] || key;
    const selectedSources = this.getFormValue('Source');
    if (Array.isArray(selectedSources) && selectedSources.length) {
      selectedSources.forEach((source: number) => {
        const sourceKey = LeadSource[source];
        const sourceValue = IntegrationSource[sourceKey as keyof typeof IntegrationSource];
        const finalValue = getMappedKey(sourceValue);
        if (Array.isArray(this.allSubSourceList[finalValue])) {
          this.subSourceList.push(...this.allSubSourceList[finalValue]);
        }
      });
      this.subSourceList = this.subSourceList.sort((a: any, b: any) => a.localeCompare(b));
    } else {
      const allSubSources = this.leadSources?.flatMap((lead: any) => {
        const cleanedKey = cleanKey(lead?.displayName || '');
        const mappedKey = getMappedKey(cleanedKey);
        return this.allSubSourceList[mappedKey] || [];
      }) || [];

      this.subSourceList = allSubSources;
    }
  }

  updatePropertyType() {
    if (
      this.getFormValue('PropertyType') &&
      this.getFormValue('PropertyType').length
    ) {
      this.propertySubTypes = this.getFormValue('PropertyType')
        ?.map(
          (type: any) =>
            this.propertyType?.find((property: any) => property.id === type)
              ?.childTypes
        )
        .flat();
    } else {
      this.propertySubTypes = this.propertyType
        ?.map((type: any) => type?.childTypes)
        .flat();
    }
  }

  OnDoneStatus(meeting: string) {
    const statuses = this.getFormValue('MeetingOrVisitStatuses');
    const updatedStatuses = statuses?.includes(meeting)
      ? statuses.filter((status: string) => status !== meeting)
      : [...(statuses ? statuses : []), meeting];

    this.filtersForm.patchValue({ MeetingOrVisitStatuses: updatedStatuses });
  }

  MeetingOrVisitdateChange() {
    if (this.DateForMeetingOrVisit?.[0]) {
      const fromDate = setTimeZoneDate(
        this.DateForMeetingOrVisit?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      const toDate = setTimeZoneDate(
        this.DateForMeetingOrVisit?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      this.filtersForm.patchValue({
        FromDateForMeetingOrVisit: fromDate,
        ToDateForMeetingOrVisit: toDate,
      });
    }
  }

  onResetDateFilter() {
    this.dateType = 'All';
    this.filterDate = [];
    this.filtersForm.patchValue({
      dateType: LeadDateType[this.dateType as keyof typeof LeadDateType],
      fromDate: null,
      toDate: null,
    });
  }

  filterTag(tag: any) {
    const customFlags = this.filtersForm.value.CustomFlags ?? [];
    const updatedFlags = customFlags.includes(tag)
      ? customFlags.filter((flag: any) => flag !== tag)
      : [...customFlags, tag];
    this.filtersForm.patchValue({ CustomFlags: updatedFlags });
  }

  trackerFeatures(visibility: any) {
    this.trackingService.trackFeature(
      `Web.Leads.filter.${visibility.replace(/\s+/g, '')}.Click`
    );
  }

  validateCarpetArea(): void {
    const minArea = this.getFormValue('MinCarpetArea');
    const maxArea = this.getFormValue('MaxCarpetArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  validateBuildUpArea(): void {
    const minArea = this.getFormValue('MinBuiltUpArea');
    const maxArea = this.getFormValue('MaxBuiltUpArea');

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.buildUpAreaValidations = false;
    } else {
      this.buildUpAreaValidations = true;
    }
  }

  saleableAreaValidation(): void {
    const mBudget = this.getFormValue('MinSaleableArea');
    const maxBudget = this.getFormValue('MaxSaleableArea');

    if (mBudget && maxBudget && maxBudget < mBudget) {
      this.saleableValidation = false;
    } else {
      this.saleableValidation = true;
    }
  }

  netAreaValidation(): void {
    const minArea = this.getFormValue('MinNetArea');
    const maxArea = this.getFormValue('MaxNetArea');

    if (minArea && maxArea && maxArea < minArea) {
      this.netValidation = false;
    } else {
      this.netValidation = true;
    }
  }
  propertyAreaValidation(): void {
    const minArea = this.getFormValue('MinPropertyArea');
    const maxArea = this.getFormValue('MaxPropertyArea');

    if (minArea && maxArea && maxArea < minArea) {
      this.areaValidation = false;
    } else {
      this.areaValidation = true;
    }
  }

  // New Possession Needed By methods
  handlePossessionNeededByChange(value: string): void {
    this.filtersForm.get('possessionNeededBy')?.setValue(value);

    if (value === 'Custom') {
      this.selectedPossessionNeededBy = 'Custom - Select Date';
      this.filtersForm.controls['customPossessionNeededByDate'].setValue(null);
      this.isValidPossessionNeededByDate = false;
      // Keep modal open for custom date selection
    } else {
      this.selectedPossessionNeededBy = value;
      this.filtersForm.controls['customPossessionNeededByDate'].setValue(null);
      this.isValidPossessionNeededByDate = false;
      // Auto-close modal for non-custom selections
      this.isOpenPossessionNeededByModal = false;
    }
  }

  closePossessionNeededBy(): void {
    if (
      this.filtersForm.controls['possessionNeededBy'].value === 'Custom' &&
      !this.filtersForm.controls['customPossessionNeededByDate'].value
    ) {
      this.isValidPossessionNeededByDate = true;
      return;
    }
    this.isOpenPossessionNeededByModal = false;
  }

  onCustomPossessionDateSelected(event: any): void {
    if (event) {
      const selectedDate = new Date(event);
      const formattedDate = selectedDate.toLocaleDateString('en-GB'); // dd/mm/yyyy format
      this.selectedPossessionNeededBy = `Custom - ${formattedDate}`;
      this.isValidPossessionNeededByDate = false;
      // Auto-close modal when custom date is selected
      this.isOpenPossessionNeededByModal = false;
    }
  }

  get minDate(): Date {
    const minDate = new Date();
    minDate.setHours(0, 0, 0, 0);
    return minDate;
  }

  onCallDateTypeChange(event: any) {
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));
    switch (event) {
      case 'Today':
        this.callDate = [new Date(date), new Date(date)];
        break;
      case 'Yesterday':
        const yesterday = new Date(date);
        yesterday.setDate(yesterday.getDate() - 1);
        this.callDate = [new Date(yesterday), new Date(yesterday)];
        break;
      case 'Last 7 Days':
        const last7 = new Date(date);
        last7.setDate(last7.getDate() - 6);
        this.callDate = [new Date(last7), new Date(date)];
        break;
      case 'Custom Date':
        this.callDate = [null, null];
        break;
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
